
paths:
  # -------------> Assign Device <-------------
  "/devices/assign":
    put:
      tags:
        - Devices
      summary: Assign a device to a Lock Station.
      description: Assign device to a Lock Station.
      parameters:
        - name: deviceId
          in: query
          description: Device Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
        - name: lockStationId
          in: query
          description: Lock Station Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
        - name: lockStationIndex
          in: query
          description: Index of the device in the lock station (starts at 1)
          required: true
          schema:
            type: integer
            example: 1

      responses:
        200:
          description: Device assigned
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error