<canvas id="ridesPerHourChart" width="400" height="200"></canvas>

<script>
  function createRidesPerHourChart() {
    const ctx = document.getElementById('ridesPerHourChart').getContext('2d');
    
    const ridesPerHourData = ridesData?.ridesPerHour || [];
    
    const hours = ridesPerHourData.map(item => `${item.hour}:00`);
    const counts = ridesPerHourData.map(item => item.count);
    
    new Chart(ctx, {
      type: 'line',
      data: {
        labels: hours,
        datasets: [{
          label: 'Rides per Hour',
          data: counts,
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Rides'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Hour of Day'
            }
          }
        }
      }
    });
  }
</script>