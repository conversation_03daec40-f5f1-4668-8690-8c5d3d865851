import express from 'express';
import authentication from '../controllers/authentication.js';
import web from '../controllers/webController.js';

const router = express.Router();

// Route for homepage
router.get('/', web.login);
router.get('/init', authentication.checkTokensValid, web.init);
router.get('/map', authentication.checkTokensValid, web.map);
router.get('/map/refresh', web.mapRefresh);
router.get('/devices', authentication.checkTokensValid, web.devices);
router.get('/analytics/rides', authentication.checkTokensValid, web.analyticsRide);  
router.post('/devices/add', authentication.checkTokensValid, web.addDevice);
router.delete('/devices/delete/:id', authentication.checkTokensValid, web.deleteDevice);
  

router.get('/tables', (req, res) => {
    res.render('tables');
  });

router.get('/datatables', (req, res) => {
    res.render('datatables');
  });

router.get('/maps', (req, res) => {
    res.render('googlemaps');
  });

export default router;