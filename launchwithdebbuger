{
    "version": "0.2.0",
    "configurations": [
        // {
        //     "type": "node",
        //     "request": "launch", // This is your existing launch configuration
        //     "name": "Launch Program",
        //     "skipFiles": [
        //         "<node_internals>/**"
        //     ],
        //     "program": "${workspaceFolder}/src/server.js"
        // },
        {
            "type": "node",
            "request": "attach", // Key change: "attach" instead of "launch"
            "name": "Attach to Node.js Debugger",
            "port": 9229,      // Specify the port your Node.js app is listening on
            "address": "localhost",
            "localRoot": "${workspaceFolder}", // Your local project root
            "remoteRoot": "/app",              // The path to your application inside the Docker container
            // "protocol": "inspector", // Typically "inspector" for modern Node.js
            "skipFiles": [
                "<node_internals>/**"
            ]
        }
    ]
}