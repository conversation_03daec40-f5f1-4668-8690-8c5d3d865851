import express from 'express';
import lockStationsModels from '../controllers/lockStationModels.js';
import validate from '../middlewares/validate.js';
import authenticate from '../middlewares/authenticate.js';

const router = express.Router();

router.post('/', authenticate.authenticateToken, lockStationsModels.newLockStationModel);
router.delete('/', authenticate.authenticateToken, lockStationsModels.deleteLockStationModel);
router.put('/', authenticate.authenticateToken, lockStationsModels.updateLockStationModel);
router.get('/', authenticate.authenticateToken, lockStationsModels.getLockStationModel);
router.get('/all', authenticate.authenticateToken, lockStationsModels.getAllLockStationModels);

export default router;