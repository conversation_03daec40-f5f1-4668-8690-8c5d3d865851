  # -------------> Rent a device <-------------
paths:
  "/devices/rent/debugCreateRent":
    post:
      tags:
        - Rent
      summary: Create a historic rent for a device (Debug only)
      requestBody:
        description: Rent information
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - startTime
                - totalTime
                - customerId
                - deviceId
                - startStationId
                - endStationId
                - paymentMethod
                - price
              properties:
                startTime:
                  type: integer
                  description: Start time as a UNIX timestamp in milliseconds
                  example: 1758555994917
                totalTime:
                  type: integer
                  description: Total duration of the rent in seconds
                  example: 3600
                customerId:
                  type: integer
                  description: ID of the customer renting the device
                  example: 4
                deviceId:
                  type: string
                  description: ID of the device being rented
                  example: "974379bd-3d32-41c7-8b19-63c06b307884"
                startStationId:
                  type: string
                  description: ID of the station where the rent starts
                  example: "19e5b344-68c2-4217-a1ba-543eff442e1c"
                endStationId:
                  type: string
                  description: ID of the station where the rent ends
                  example: "19e5b344-68c2-4217-a1ba-543eff442e1c"
                paymentMethod:
                  type: string
                  description: Payment method used for the rent
                  example: "Credit"
                price:
                  type: number
                  description: Total price of the rent
                  example: 10.99
      responses:
        200:
          description: Device rented
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        408:
          description: Request timeout
        500:
          description: Internal server error