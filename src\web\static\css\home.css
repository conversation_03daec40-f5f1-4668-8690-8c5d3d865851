:root {
    --station: #1572E8;
    --station-offline: #bbbbbb;
    --station-partial-service: #ff0000;
    --bike: #31CE36;
    --bike-offline: #bbbbbb;
    --user: #48ABF7;
    --active-rides: #FFAD46;
}

.marker-cluster div {
    background-color: inherit;
    border-radius: 20px;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: white;
    font-weight: bold;
}

/* Station CSS */
.bg-station {
    background-color: var(--station);
}

.marker-cluster.bg-station div {
    background-color: var(--station);
}

.text-station {
    color: var(--station);
}

#stationsSwitch.form-check-input:checked {
    background-color: var(--station);
    border-color: var(--station);
}

#stationsSwitch.form-check-input {
    border-color: var(--station);
}

/* Bike CSS */
.bg-bike {
    background-color: var(--bike);
}

.marker-cluster.bg-bike div {
    background-color: var(--bike);
}

.text-bike {
    color: var(--bike);
}

#devicesSwitch.form-check-input:checked {
    background-color: var(--bike);
    border-color: var(--bike);
}

#devicesSwitch.form-check-input {
    border-color: var(--bike);
}

/* User CSS */
.bg-user {
    background-color: var(--user);
}

.text-user {
    color: var(--user);
}

/* Active rides CSS */
.bg-active-rides {
    background-color: var(--active-rides);
}

.text-active-rides {
    color: var(--active-rides);
}