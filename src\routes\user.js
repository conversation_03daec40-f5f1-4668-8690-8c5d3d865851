import express from 'express';
import usersController from '../controllers/users.js';
import authenticate from '../middlewares/authenticate.js';
import validate from '../middlewares/validate.js';

const router = express.Router();

router.post('/', validate.validate_new_user, usersController.newUser);
router.post('/login', usersController.loginUser);
router.get('/', authenticate.authenticateToken, usersController.getUsers);
router.get('/count', authenticate.authenticateToken, usersController.getTotalUsers);
router.post('/changepassword', authenticate.authenticateToken, usersController.changePassword);
router.delete('/', authenticate.authenticateToken, usersController.deleteUser);
router.post('/refresh', usersController.refreshToken);

//Configuration for tenant payment method
router.get('/configuration', authenticate.authenticateToken, usersController.getTenantConfiguration);
router.post('/configuration', authenticate.authenticateToken, usersController.configureTenant);
//Wallet
router.get('/wallet', authenticate.authenticateToken, usersController.getWallet);
router.post('/wallet', authenticate.authenticateToken, usersController.addCurrencyToWallet);
//OAUTH2
router.get('/google', usersController.authGoogleInit);
router.get('/google/callback', usersController.authGoogleCallback);

export default router;