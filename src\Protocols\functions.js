import Omnicommands from "./Omni/device/commands.js";

const pendingResponses = new Map();

async function sendUnlock(device, userid) {
    if (device.relatedModel.protocol == "OMNI") {
        return Omnicommands._lockorUnlockRequestCommand(device, userid);
    }
    return { code: 401, error: "Device protocol not found" };
}
async function sendLock(device, userid) {
    if (device.relatedModel.protocol == "OMNI") {
        return Omnicommands._lockorUnlockRequestCommand(device, userid);
    }
    return { code: 401, error: "Device protocol not found" };
}

export default { sendUnlock, sendLock };
