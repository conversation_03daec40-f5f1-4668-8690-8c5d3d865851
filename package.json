{"name": "bikesharing", "version": "1.0.0", "description": "", "main": "src/server.js", "type": "module", "scripts": {"start": "nodemon ./src/server.js", "debug": "nodemon --inspect-brk=0.0.0.0:9229 ./src/server.js"}, "repository": {"type": "git", "url": "git+https://gitlab02.mirgor.com.ar:ADV/IoT/bikesharing/backend.git"}, "keywords": [], "author": "Comasco Facundo", "license": "ISC", "homepage": "https://gitlab02.mirgor.com.ar:ADV/IoT/bikesharing#readme", "dependencies": {"axios": "^1.9.0", "bcrypt": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.2", "google-auth-library": "^9.15.1", "googleapis": "^149.0.0", "hbs": "^4.2.0", "jsonwebtoken": "^9.0.2", "mercadopago": "^2.8.0", "nodemon": "^3.1.9", "path": "^0.12.7", "pg": "^8.13.1", "pg-hstore": "^2.3.4", "sequelize": "^6.37.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "validator": "^13.15.15"}}