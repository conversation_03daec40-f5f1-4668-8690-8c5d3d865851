paths:
  # -------------> Ref<PERSON> <-------------
  "/users/changepassword":
    post:
      security:
        - bearerAuth: []
      tags:
        - Users
      summary: Change password
      description: Change user password.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
                  description: Password
                newpassword:
                  type: string
                  description: New Password

      responses:
        200:
          description: Password changed successfully
        400:
          description: Bad request
        401:
          description: Unauthorized
        500:
          description: Internal server error

components:
  schemas:
    User:
      type: object
      required:
        - name
        - surname
        - username
        - password
        - mail
      properties:        
        name:
          type: string
          description: First name of the user
          example: Facundo
        surname:
          type: string
          description: Last name of the user
          example: Comasco
        username:
          type: string
          description: Unique username
          example: facucomasco
        password:
          type: string
          description: Password of the user
          example: abcdefg
        mail:
          type: string
          description: Email of the user
          example: "<EMAIL>"
