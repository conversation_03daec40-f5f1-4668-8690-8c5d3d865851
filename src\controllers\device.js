import sequelize from '../config/database.js';
import deviceAssign from '../functions/deviceAssign.js';
import authenticate from '../middlewares/authenticate.js';
import validate from '../middlewares/validate.js';
import Device from '../models/device.js';
import DeviceModels from '../models/deviceModels.js';
import LockStation from '../models/lockStations.js';
import Station from '../models/station.js';
import Telemetry from '../models/telemetry.js';

const newDevice = async (req, res) => {
    try {
        const user = req.user;
        const new_device = req.body;

        if (!authenticate.hasAuthority("Tenant", user)) return res.status(400).send({ error: "Only tenant users are allowed to create devices" });

        if (!new_device.serial) return res.status(400).send({ error: "Serial not provided" });
        if (!new_device.phone) return res.status(400).send({ error: "No phone provided" });
        if (!new_device.type) return res.status(400).send({ error: "No type provided" });
        if (!new_device.modelId) return res.status(400).send({ error: "No modelId provided" });
        if (!new_device.qrCode) return res.status(400).send({ error: "QR code not provided" });

        if (!validate.isUUID(new_device.modelId)) return res.status(400).send({ error: "Model Id is not an uuid" });
        const Model = await DeviceModels.findByPk(new_device.modelId);
        if (!Model) return res.status(400).send({ error: "Model not found" });

        new_device.status = "Unknown";
        new_device.tenantId = user.id;
        new_device.locking_parameters = {};
        new_device.settings = {};

        const devicecreated = await Device.create(new_device);
        if(!devicecreated)return res.status(500).json(devicecreated);
        return res.status(200).json(devicecreated);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getDevice = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (!id) return res.status(400).send({ error: "id not provided" });
        if (!validate.isUUID(id)) return res.status(400).send({ error: "id provided is not an uuid" })
        
        const device = await Device.findByPk(id, {
            include: [
                {
                    model: DeviceModels,
                    as: 'relatedModel'
                },
                {
                    model: LockStation,
                    as: 'relatedLockStation',
                    include: {
                        model: Station,
                        as: 'relatedStation'
                    }
                }
            ]
        });

        if (!device) {
            return res.status(404).json({ error: "device not found" });
        }
        device.dataValues.protocol = device.relatedModel.protocol;
        device.dataValues.autonomy = await _getDeviceAutonomyObject(device);

        return res.status(200).json(device);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getDeviceBySerial = async (req, res) => {
    try {
        const user = req.user;
        const { serial } = req.query;

        if (!serial) return res.status(400).send({ error: "serial not provided" });

        const device = await Device.findOne({
            where: { serial: serial },
        });

        if (!device) return res.status(404).json({ error: "device not found" });
        const deviceModel = await device.getRelatedModel();
        device.dataValues.protocol = deviceModel.protocol;
        device.dataValues.autonomy = await _getDeviceAutonomyObject(device);

        return res.status(200).json(device);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getDeviceByQrCode = async (req, res) => {
    try {
        const user = req.user;
        const { qrCode } = req.query;

        if (!qrCode) return res.status(400).send({ error: "Qr code not provided" });

        const device = await Device.findOne({
            where: { qrCode: qrCode },
        });

        if (!device) return res.status(404).json({ error: "device not found" });
        const deviceModel = await device.getRelatedModel();
        device.dataValues.protocol = deviceModel.protocol;
        device.dataValues.autonomy = await _getDeviceAutonomyObject(device);

        return res.status(200).json(device);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getAllDevices = async (req, res) => {
    try {
        const user = req.user;

        var devices = await _getAllDevices(user);

        for (const device of devices) {
            const deviceModel = await device.getRelatedModel();
            device.dataValues.protocol = deviceModel.protocol;
        }

        return res.status(200).json(devices);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getPositionAllDevices = async (req, res) => {
    try {
        // const latestPositions = await Telemetry.findAll({
        //     where: {
        //         event: 'Position'
        //     },
        //     attributes: { 
        //         exclude: ['id'] // Exclude id if you don't need it
        //     },
        //     order: [
        //         ['userId', 'ASC'],
        //         ['ts', 'DESC']
        //     ],
        //     // PostgreSQL specific DISTINCT ON to get one record per userId
        //     group: [sequelize.literal('"deviceId", "id"')], // Include all non-aggregated columns
        //     having: sequelize.literal('"ts" = MAX("ts")')
        // });

        // Alternative DISTINCT ON syntax (PostgreSQL specific)
        const latestPositionsAlt = await sequelize.query(
            `SELECT DISTINCT ON ("deviceId") * 
             FROM "Telemetries" 
             WHERE event = 'Position' 
             ORDER BY "deviceId", "ts" DESC`,
            {
                model: Telemetry,
                mapToModel: true
            }
        );

        return res.status(200).json(latestPositionsAlt);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};

const getAllDevicesWithAutonomy = async (req, res) => {
    try {
        const user = req.user;

        var devices = await _getAllDevices(user);
        if (devices.length === 0) {
            return res.status(200).json([]);
        }
        
        // Search all the autonomies at one, to avoid multiple database requests
        // It is more efficient than making each request in the loop
        const deviceIds = devices.map(device => device.id);
        const latestAutonomies = await sequelize.query(`
            SELECT t."deviceId", t."battery_level"
            FROM "Telemetries" t
            INNER JOIN (
                SELECT "deviceId", MAX("createdAt") AS maxCreatedAt
                FROM "Telemetries"
                WHERE "deviceId" IN (:deviceIds)
                GROUP BY "deviceId"
            ) latest
            ON t."deviceId" = latest."deviceId" AND t."createdAt" = latest.maxCreatedAt
            `, {
            replacements: { deviceIds },
            type: sequelize.QueryTypes.SELECT
        });

        const autonomyMap = new Map();
        for (const autonomy of latestAutonomies) {
            autonomyMap.set(autonomy.deviceId, autonomy);
        }

        for (const device of devices) {
            const autonomy = autonomyMap.get(device.id);
            const deviceModel = await device.getRelatedModel()
            device.dataValues.protocol = deviceModel.protocol;
            device.dataValues.autonomy = autonomy
                ? await _getDeviceAutonomyObject(device, autonomy.battery_level)
                : null;
        }

        return res.status(200).json(devices);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getDeviceAutonomy = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (!id) return res.status(400).send({ error: "id not provided" });
        if (!validate.isUUID(id)) return res.status(400).send({ error: "id provided is not an uuid" })
        
        const device = await Device.findByPk(id, {
            include: [
                {
                    model: Telemetry,
                    as: 'Telemetries',
                    attributes: ['id', 'createdAt', 'battery_level'],
                    order: [['createdAt', 'DESC']],
                    limit: 1,
                    separate: true, // It can avoid problems in limiting and ordering
                }
            ]
        });

        if (!device) {
            return res.status(404).json({ error: "device not found" });
        }
        if (device.Telemetries.length === 0) {
            return res.status(400).json({ error: "no autonomy data in the device" });
        }

        var batteryLevel = device.Telemetries[0].dataValues.battery_level;
        const autonomy = await _getDeviceAutonomyObject(device, batteryLevel);

        return res.status(200).json(autonomy);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const updateDevice = async (req, res) => {
    try {
        const user = req.user;
        const update_device = req.body;
        
        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to update devices" });
        if (!update_device.id) return res.status(400).send({ error: "Lock stations id not provided" });
        if (!validate.isUUID(update_device.id)) return res.status(400).send({ error: "id is not a valid uuid" });

        var device = await Device.findByPk(update_device.id);
        if (!device) return res.status(404).send({ error: "Device not found" });

        if (update_device.modelId != undefined) {
            if (!validate.isUUID(update_device.modelId)) return res.status(400).send({ error: "modelId is not a valid uuid" });
            const deviceModel = await DeviceModels.findByPk(update_device.modelId);
            if (!deviceModel) return res.status(400).send({ error: "Device Model not found" });
            if (deviceModel.tenantId != user.id) return res.status(400).send({ error: "You are not the owner of the device model" });
            device.modelId = update_device.modelId;
        }
        if (update_device.serial) {
            device.serial = update_device.serial;
        }
        if (update_device.qrCode) {
            device.qrCode = update_device.qrCode;
        }
        if (update_device.type) {
            device.type = update_device.type;
        }
        if (update_device.phone) {
            device.phone = update_device.phone;
        }
        if (update_device.description) {
            device.description = update_device.description;
        }
        await device.save();
        return res.status(200).json(device);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const deleteDevice = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (!id) return res.status(400).send({ error: "id not provided" });
        if (user.type != 'Tenant') return res.status(401).send({ error: "Only tenant users are allowed to delete devices" });

        var device = await Device.findByPk(id);
        // Given the Id also find the telemeytry and delete it
        var telemetry = await Telemetry.findOne({ where: { deviceId: id } });
        if (telemetry) await telemetry.destroy();

        if (!device) return res.status(401).send({ error: "Device not found" });
        if (device.tenantId != user.id) return res.status(401).send({ error: "Device delete not permitted" });
        await device.destroy();
        return res.status(200).send();

    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const assingDevice = async (req, res) => {
    try {
        const user = req.user;
        const { deviceId } = req.query;
        const { lockStationId } = req.query;
        const lockStationIndex = parseInt(req.query.lockStationIndex, 10);

        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to assign devices" });

        await deviceAssign.returnDeviceToStation(user.id, deviceId, lockStationId, lockStationIndex);

        return res.status(200).send({ message: "Device assigned" });
    } catch (error) {
        res.status(error.statusCode).json({ error: error.message });
    }
}

const unassignDevice = async (req, res) => {
    try {
        const user = req.user;
        const { deviceId } = req.query;

        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to assign devices" });

        await deviceAssign.assignDeviceToUser(user.id, deviceId);

        return res.status(200).send({ message: "Device unassigned" });
    } catch (error) {
        res.status(error.statusCode).json({ error: error.message });
    }
}

async function _getAllDevices(user) {
    var devices = {};
    if (authenticate.hasAuthority("Tenant", user)) {
        devices = await Device.findAll({
            where: {
                tenantId: user.id,
            },
        });
    }
    else if (authenticate.hasAuthority("Customer", user)) {

        devices = await Device.findAll({
            where: {
                tenantId: user.tenantId,
                customerId: user.id
            },
        });
    }
    else if (authenticate.hasAuthority("Sysadmin", user)) {
        devices = await Device.findAll();
    }
    return devices;
}

async function _getDeviceAutonomyObject(device) {
    try {
        const telemetry = await Telemetry.findOne({
            where: { deviceId: device.id },
            attributes: ['id', 'createdAt', 'deviceId', 'battery_level'],
            order: [['createdAt', 'DESC']],
            limit: 1,
            separate: true, // It can avoid problems in limiting and ordering
        });
    
        // TODO: Get the efficiency
        const deviceBatteryEfficiency = 0.3;
    
        var batteryLevel = telemetry.battery_level;
        var kilometers = batteryLevel * deviceBatteryEfficiency;
        // round to 1 digit
        kilometers = parseFloat(kilometers.toFixed(1));
    
        return {
            batteryLevel: batteryLevel,
            kilometers: kilometers
        };
    } catch (error) {
        return null;
    }
}

export default {
    newDevice, getDevice, getDeviceBySerial, getDeviceByQrCode, deleteDevice,
    updateDevice, getPositionAllDevices, getAllDevices, getAllDevicesWithAutonomy,
    getDeviceAutonomy, assingDevice, unassignDevice
};
