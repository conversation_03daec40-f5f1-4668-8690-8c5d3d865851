<!-- Sidebar -->
<div class="sidebar" data-background-color="dark">
<div class="sidebar-logo">
    <!-- <PERSON><PERSON> Header -->
    <div class="logo-header d-flex justify-content-center align-items-center" data-background-color="dark">
        <a href="/" class="logo">
            <img
                src="/assets/img/mirgor/mirgor_logo_white.png"
                alt="navbar brand"
                class="navbar-brand"
                height="45"
            />
        </a>
        <div class="nav-toggle">
            <button class="btn btn-toggle toggle-sidebar">
                <i class="gg-menu-right"></i>
            </button>
            <button class="btn btn-toggle sidenav-toggler">
                <i class="gg-menu-left"></i>
            </button>
        </div>
        <button class="topbar-toggler more">
            <i class="gg-more-vertical-alt"></i>
        </button>
    </div>
    <!-- End Logo Header -->
</div>
<div class="sidebar-wrapper scrollbar scrollbar-inner">
    <div class="sidebar-content">
    <ul class="nav nav-secondary">

        <li class="nav-item <%= activePage === 'map' ? 'active' : '' %>">
            <a href="/map">
                <i class="fas fa-home"></i>
                <p>Home</p>
            </a>
        </li>

        <li class="nav-item <%= activeGroup === 'stations' ? 'active' : '' %>">
            <a
                data-bs-toggle="collapse"
                href="#stations"
                class="collapsed"
                aria-expanded="false"
            >
                <i class="fas fa-charging-station"></i>
                <p>Stations</p>
                <span class="caret"></span>
            </a>
            <div class="collapse <%= activeGroup === 'stations' ? 'show' : '' %>" id="stations">
                <ul class="nav nav-collapse">
                    <li class="<%= activePage === 'stationsList' ? 'active' : '' %>">
                        <a href="/stations/list">
                            <span class="sub-item">Stations list</span>
                        </a>
                    </li>
                    <li class="<%= activePage === 'lockStationsList' ? 'active' : '' %>">
                        <a href="/stations/lockstationslist">
                            <span class="sub-item">Lock Stations list</span>
                        </a>
                    </li>
                    <li class="<%= activePage === 'lockStationsModels' ? 'active' : '' %>">
                        <a href="/stations/lockstationmodels">
                            <span class="sub-item">Lock Station models</span>
                        </a>
                    </li>
                </ul>
            </div>
        </li>

        <li class="nav-item <%= activeGroup === 'vehicles' ? 'active' : '' %>">
            <a
                data-bs-toggle="collapse"
                href="#vehicles"
                class="collapsed"
                aria-expanded="false"
            >
                <i class="fas fa-bicycle"></i>
                <p>Vehicles</p>
                <span class="caret"></span>
            </a>
            <div class="collapse <%= activeGroup === 'vehicles' ? 'show' : '' %>" id="vehicles">
                <ul class="nav nav-collapse">
                    <li class="<%= activePage === 'devices' ? 'active' : '' %>">
                        <a href="/devices">
                            <span class="sub-item">Vehicles list</span>
                        </a>
                    </li>
                    <li class="<%= activePage === 'vehiclesModels' ? 'active' : '' %>">
                        <a href="/vehicles/models">
                            <span class="sub-item">Vehicle models</span>
                        </a>
                    </li>
                </ul>
            </div>
        </li>

        <li class="nav-item <%= activePage === 'users' ? 'active' : '' %>">
            <a href="/users">
                <i class="fas fa-users"></i>
                <p>Users</p>
            </a>
        </li>

        <li class="nav-item <%= activeGroup === 'remoteControl' ? 'active' : '' %>">
            <a
                data-bs-toggle="collapse"
                href="#remotecontrol"
                class="collapsed"
                aria-expanded="false"
            >
                <i class="fas fa-tv"></i>
                <p>Remote Control</p>
                <span class="caret"></span>
            </a>
            <div class="collapse <%= activeGroup === 'remoteControl' ? 'show' : '' %>" id="remotecontrol">
                <ul class="nav nav-collapse">
                    <li class="<%= activePage === 'remoteStations' ? 'active' : '' %>">
                        <a href="/remote/stations">
                            <span class="sub-item">Stations</span>
                        </a>
                    </li>
                    <li class="<%= activePage === 'remoteVehicles' ? 'active' : '' %>">
                        <a href="/remote/vehicles">
                            <span class="sub-item">Vehicles</span>
                        </a>
                    </li>
                    <li class="<%= activePage === 'remotetimeschedule' ? 'active' : '' %>">
                        <a href="/remote/timeschedule">
                            <span class="sub-item">Time schedule (coming soon)</span>
                        </a>
                    </li>
                    <li class="<%= activePage === 'remotegeofence' ? 'active' : '' %>">
                        <a href="/remote/geofence">
                            <span class="sub-item">Geo fence (coming soon)</span>
                        </a>
                    </li>
                </ul>
            </div>
        </li>

        <li class="nav-item <%= activeGroup === 'analytics' ? 'active' : '' %>">
            <a
                data-bs-toggle="collapse"
                href="#analytics"
                class="collapsed"
                aria-expanded="false"
            >
                <i class="fas fa-chart-pie"></i>
                <p>Analytics</p>
                <span class="caret"></span>
            </a>
            <div class="collapse <%= activeGroup === 'analytics' ? 'show' : '' %>" id="analytics">
                <ul class="nav nav-collapse">
                    <li class="<%= activePage === 'analyticsRides' ? 'active' : '' %>">
                        <a href="/analytics/rides">
                            <span class="sub-item">Rides</span>
                        </a>
                    </li>
                </ul>
            </div>
        </li>
    </ul>
    </div>
</div>
</div>
<!-- End Sidebar -->