
paths:
  # -------------> Delete Lock Station Model <-------------
  "/lockstations/models":
    delete:
      tags:
        - Lock Stations MODELS
      summary: Delete a lock station MODELS.
      description: Delete a lock station MODEL. Only tenants can delete lock stations MODELS.
      parameters:
        - name: id
          in: query
          description: Model Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
      responses:
        200:
          description: Lock Station model deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LockStationModel'
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error