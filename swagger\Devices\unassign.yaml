
paths:
  # -------------> Unassign Device <-------------
  "/devices/unassign":
    put:
      tags:
        - Devices
      summary: Unassign a device from a Lock Station.
      description: Unassign device from a Lock Station.
      parameters:
        - name: deviceId
          in: query
          description: Device Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"

      responses:
        200:
          description: Device unassigned
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error