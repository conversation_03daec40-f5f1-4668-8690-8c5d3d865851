import { DataTypes, Sequelize } from 'sequelize';
import sequelize from '../config/database.js';

const Rents = sequelize.define('Rents', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  date: { type: DataTypes.DATE, allowNull: false, defaultValue: Sequelize.NOW },
  startStation: { type: DataTypes.UUID, allowNull: false },
  startTime: { type: DataTypes.DATE, allowNull: false },
  endStation: { type: DataTypes.UUID, allowNull: true },
  endTime: { type: DataTypes.DATE, allowNull: true },
  totalTime: { type: DataTypes.INTEGER, allowNull: true },
  price: { type: DataTypes.DOUBLE, allowNull: true },
  paymentMethod: { type: DataTypes.STRING, allowNull: false },
  description: { type: DataTypes.STRING, allowNull: true }
});

export default Rents;