import app from './app.js';
import sequelize from './config/database.js';
import dataloader from './defaultdata/dataloader.js';

// import '../src/models/movieactors.js';
// // import '../src/models/serieactors.js';
// import '../src/models/associations.js'
import '../src/models/associations.js';
import '../src/models/configuration.js';
import '../src/models/lockStations.js';
import '../src/models/lockStationsModels.js';
import '../src/models/notifications.js';
import '../src/models/rents.js';
import '../src/models/server.js';
import '../src/models/station.js';
import '../src/models/telemetry.js';
import '../src/models/users.js';
import './models/device.js';
import './models/deviceModels.js';

import '../src/Protocols/Omni/device/server.js';
import './Protocols/Omni/device/serverDebug.js';
import './Protocols/Omni/lockStation/server.js';

import runTest from '../test/runTest.js';
import versionUpdater from './defaultdata/versionupgrader.js';

// Servidor REST

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${process.env.PORT}`);
});

sequelize.sync({ force: false })
  .then(() => {
    console.log('✅ Database sync OK')
    versionUpdater();
    dataloader();
    setTimeout(() => {
      runTest();
    }, 2000); 
  })
  .catch(err => console.error('❌ Error on DB sync:', err));

process.on('SIGINT', async () => {
    console.log('🔻 Closing database connection...');
    await sequelize.close();
    console.log('✅ Connection closed.');
    process.exit(0);
 });