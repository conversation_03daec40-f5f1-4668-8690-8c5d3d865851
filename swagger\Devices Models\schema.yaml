components:
  schemas:
    DeviceModel:
      type: object
      required:
        - name
        - model
        - protocol
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
          description: Unique identifier for the device model
          example: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        name:
          type: string
          description: Name of the device model
          example: IOT Omni 320
        model:
          type: string
          description: Device model reference
          example: "TMP-A1"
        protocol:
          type: string
          description: Communication protocol used by the device
          example: OMNI
        tenantId:
          type: string
          format: uuid
          description: Tenant identifier associated with this device model
          example: "2"
          readOnly: true
        description:
          type: string
          description: Additional details or description of the device model
          example: IOT devices from OMNI
