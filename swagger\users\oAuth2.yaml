paths:
  # -------------> Refresh <PERSON> <-------------
  "/users/google/":
    get:
      security: []
      tags:
        - Users
      summary: Google oAuth2
      description: Authenticate with google
      
      responses:
        200:
          description: Successful login
        400:
          description: Bad request
        500:
          description: Internal server error

components:
  schemas:
    User:
      type: object
      required:
        - name
        - surname
        - username
        - password
        - mail
      properties:        
        name:
          type: string
          description: First name of the user
          example: Facundo
        surname:
          type: string
          description: Last name of the user
          example: Comasco
        username:
          type: string
          description: Unique username
          example: facucomasco
        password:
          type: string
          description: Password of the user
          example: abcdefg
        mail:
          type: string
          description: Email of the user
          example: "<EMAIL>"

#  Successfully obtained tokens: {
#    access_token: '******************************************************************************************************************************************************************************************************************************',
#    refresh_token: '1//0hsdk8Zpdr3cdCgYIARAAGBESNwF-L9Ir54O6Kwo5C-qS6uV0Qj3MHhGqX1xZvUQcC1CVhhrwnu7-Uf9c7crOi7Tu9uWjna0daLs',
#    scope: 'https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile openid',
#    token_type: 'Bearer',
#    id_token: 'eyJhbGciOiJSUzI1NiIsImtpZCI6ImJhYTY0ZWZjMTNlZjIzNmJlOTIxZjkyMmUzYTY3Y2M5OTQxNWRiOWIiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mnCxP-uZnHtrTNFqxxx9Mm4MUWQTgRS4keYB0p0WsEPMmwTZ2mpLGNB45I6i-C0pQ9J4dC117ZBqgZvpMnnFvJOk-78A0sYOI-PrbmGDlYvjrDpEFzZeMjlgPDHV6BlCC58bCE8AvuqEJDQ4JBCAjNVjJVtDTpp3McqcrMMLcfF3V63IGqWBJYxi3UbUsozKdtiRaQYmZvWfMDnTMV66VayzVY2fvlPdwYALERNlxrMWaycWKKIAiCpMtK7AR6h_6rkFhBlWGVNeyoXW43fvW1XLheLJuuQCgdEj640w_ul7tZSzRIpERoHXaBmclIr0ZtgsI6no5aXG3nSTgdNRgA',
#    expiry_date: *************
#  }
#  Refresh token obtained. Store this securely: 1//0hsdk8Zpdr3cdCgYIARAAGBESNwF-L9Ir54O6Kwo5C-qS6uV0Qj3MHhGqX1xZvUQcC1CVhhrwnu7-Uf9c7crOi7Tu9uWjna0daLs
#  User Profile: {
#    id: '111253345673082247837',
#    email: '<EMAIL>',
#    verified_email: true,
#    name: 'Facundo Manuel',
#    given_name: 'Facundo',
#    family_name: 'Manuel',
#    picture: 'https://lh3.googleusercontent.com/a/ACg8ocL4rlJY45wAi33ad9ugclv8Luq-rLTTw_HYX-mTdbvNkHPIHcd3=s96-c'
#  }