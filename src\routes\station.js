import express from 'express';
import stationsController from '../controllers/stations.js';
import authenticate from '../middlewares/authenticate.js';

const router = express.Router();

router.post('/', authenticate.authenticateToken, stationsController.newStation);
router.delete('/', authenticate.authenticateToken, stationsController.deleteStation);
router.put('/', authenticate.authenticateToken, stationsController.updateStation);
router.get('/', authenticate.authenticateToken, stationsController.getStation);
router.get('/devices', authenticate.authenticateToken, stationsController.getStationDevices);
router.get('/devices/withAutonomy', authenticate.authenticateToken, stationsController.getStationDevicesWithAutonomy);
router.get('/relations', authenticate.authenticateToken, stationsController.getstationswithrelations);
router.get('/all', authenticate.authenticateToken, stationsController.getAllStations);

export default router;