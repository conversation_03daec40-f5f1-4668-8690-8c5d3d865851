import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const Device = sequelize.define('Device', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  serial: { type: DataTypes.STRING, allowNull: false, unique:true },
  qrCode: { type: DataTypes.STRING, allowNull: false, unique:true },
  phone: { type: DataTypes.STRING, allowNull: false , unique:true},//For communication and tracking
  type: {
    type: DataTypes.ENUM,
    values: ['Bike20', 'Bike26', 'Bike29', 'Scooter'],
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM,
    values: ['Ready', 'Requesting', 'Waiting', 'Unlocking', 'Locking', 'Rented', 'Repairing', 'Unknown'],
    allowNull: true,
    defaultValue: 'Unknown'
  },
  stationIndex: { type: DataTypes.INTEGER, allowNull: true },
  locking_parameters: {
    type: DataTypes.JSON,
    allowNull: true,
    get() {
      return JSON.parse(this.getDataValue("locking_parameters"));
    },
    set(value) {
      return this.setDataValue("locking_parameters", JSON.stringify(value));
    }
  },
  description: { type: DataTypes.STRING, allowNull: true },
  connected: { type: DataTypes.BOOLEAN, allowNull: true },
  settings: {
    type: DataTypes.JSON,
    allowNull: true,
    get() {
      return JSON.parse(this.getDataValue("settings"));
    },
    set(value) {
      return this.setDataValue("settings", JSON.stringify(value));
    }
    // In OMNI device Accelerometer sensitivity, upload during unlocked status, Heartbeat upload interval, upload interval during unlocked
    //Headligth, speedmode, throttle, tailling flash, cruise control
  }
});

export default Device;