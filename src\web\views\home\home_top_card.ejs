<div class="card p-0">
  <div class="row g-0" style="height: auto;">
    <div class="col-md-auto">
      <div class="d-flex justify-content-center align-items-center rounded-start <%= bgColor %>"
          style="height: 100%; width: 80px;">
        <i class="fas <%= iconName %> fa-2x <%= iconColor %>"></i>
      </div>
    </div>
    <div class="col">
      <div class="card-body">
        <h5 class="card-title mb-1">
          <%= label %>
        </h5>
        <% const defaultValue = '--'; %>
        <h5 class="card-text" data-key="<%= key %>">
          <%= defaultValue %>
        </h5>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const key = "<%= key %>";
    const el = document.querySelector(`[data-key="${key}"]`);
    const defaultValue = "<%= defaultValue %>";

    window.App.subscribe(key, (changed) => {
      const value = changed[key];

      if (!el) return;

      if (value === null || value === undefined) {
        el.textContent = defaultValue;
      } else if (Array.isArray(value)) {
        el.textContent = value.length;
      } else {
        el.textContent = value;
      }
    });
  });
</script>