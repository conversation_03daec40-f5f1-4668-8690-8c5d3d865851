import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const DevicesModel = sequelize.define('DevicesModels', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    name: { type: DataTypes.STRING, allowNull: false },
    model: { type: DataTypes.STRING, allowNull: true },
    protocol: { type: DataTypes.STRING, allowNull: false },
    description: { type: DataTypes.STRING, allowNull: true }

});

export default DevicesModel;