import Configuration from './configuration.js';
import Devices from './device.js';
import DevicesModel from './deviceModels.js';
import LockStation from './lockStations.js';
import LockStationsModel from './lockStationsModels.js';
import Rents from './rents.js';
import Stations from './station.js';
import Telemetry from './telemetry.js';
import User from './users.js';
import Wallet from './wallet.js';

// -------------- RELACIONES ENTRE ESTACIONES Y LOCK STATIONS --------------

// Una estación tiene muchas lock stations
Stations.hasMany(LockStation, {
    foreignKey: 'stationId',
    as: 'relatedLockStations'
  });
  
  // Una lock station pertenece a una estación
  LockStation.belongsTo(Stations, {
    foreignKey: 'stationId',
    as: 'relatedStation'
  });
  
  
  // -------------- RELACIÓN ENTRE LOCK STATION MODEL Y LOCK STATIONS --------------
  
  // Un modelo de lock station puede ser usado por muchas lock stations
  LockStationsModel.hasMany(LockStation, {
    foreignKey: 'modelId',
    as: 'relatedLockStations'
  });
  
  // Una lock station pertenece a un modelo
  LockStation.belongsTo(LockStationsModel, {
    foreignKey: 'modelId',
    as: 'relatedModel'
  });
  
  
  // -------------- RELACIÓN ENTRE DEVICE MODEL Y DEVICES --------------
  
  // Un modelo de dispositivo puede ser usado por muchos dispositivos
  DevicesModel.hasMany(Devices, {
    foreignKey: 'modelId',
    as: 'relatedDevices'
  });
  
  // Un dispositivo pertenece a un modelo
  Devices.belongsTo(DevicesModel, {
    foreignKey: 'modelId',
    as: 'relatedModel'
  });
  
  
  // -------------- RELACIÓN ENTRE LOCK STATION Y DEVICES --------------
  
  // Una lock station tiene muchos dispositivos
  LockStation.hasMany(Devices, {
    foreignKey: 'LockStationId',
    as: 'relatedDevices'
  });
  
  // Un dispositivo pertenece a una lock station
  Devices.belongsTo(LockStation, {
    foreignKey: 'LockStationId',
    as: 'relatedLockStation'
  });
  
  
  // -------------- RELACIONES CON TENANT (type: 'Tenant' en User) --------------
  
  // Dispositivos pertenecen a un tenant
  Devices.belongsTo(User, {
    foreignKey: 'tenantId',
    as: 'tenant'
  });
  User.hasMany(Devices, {
    foreignKey: 'tenantId',
    as: 'devices'
  });
  
    // Dispositivos pertenecen a un customer
    Devices.belongsTo(User, {
        foreignKey: 'customerId',
        as: 'customer'
      });
    User.hasMany(Devices, {
        foreignKey: 'customerId',
        as: 'devicesrented'
    });

  // Lock stations pertenecen a un tenant
  LockStation.belongsTo(User, {
    foreignKey: 'tenantId',
    as: 'tenant'
  });
  User.hasMany(LockStation, {
    foreignKey: 'tenantId',
    as: 'lockStations'
  });
  
  // Stations pertenecen a un tenant
  Stations.belongsTo(User, {
    foreignKey: 'tenantId',
    as: 'tenant'
  });
  User.hasMany(Stations, {
    foreignKey: 'tenantId',
    as: 'stations'
  });
  
  // Lock Station Models pertenecen a un tenant
  LockStationsModel.belongsTo(User, {
    foreignKey: 'tenantId',
    as: 'tenant'
  });
  User.hasMany(LockStationsModel, {
    foreignKey: 'tenantId',
    as: 'lockStationModels'
  });
  
  // Device Models pertenecen a un tenant
  DevicesModel.belongsTo(User, {
    foreignKey: 'tenantId',
    as: 'tenant'
  });
  User.hasMany(DevicesModel, {
    foreignKey: 'tenantId',
    as: 'deviceModels'
  });
  
  
  // -------------- RELACIÓN DE USUARIO CON TENANT --------------
  
  // Usuarios (customers) pertenecen a un tenant
  User.belongsTo(User, {
    foreignKey: 'tenantId',
    as: 'tenant'
  });
  User.hasMany(User, {
    foreignKey: 'tenantId',
    as: 'customers'
  });
  
  // -------------- RELACIONES ENTRE TELEMETRIAS Y DEVICES --------------

// Un device tiene muchas telemetrias
Devices.hasMany(Telemetry, {
  foreignKey: 'deviceId',
  as: 'Telemetries'
});

// Una telemetria pertenece a un device
Telemetry.belongsTo(Devices, {
  foreignKey: 'deviceId',
  as: 'Device'
});

  // -------------- RELACIONES ENTRE TELEMETRIAS Y LOCK STATION --------------

// Una estacion tiene muchas telemetrias
LockStation.hasMany(Telemetry, {
  foreignKey: 'lockStationId',
  as: 'LockStationTelemetries'
});

// Una telemetria pertenece a una estación
Telemetry.belongsTo(LockStation, {
  foreignKey: 'lockStationId',
  as: 'TelemetryLockStation'
});

  // -------------- RELACIONES ENTRE TELEMETRIAS Y STATION --------------

// Una estacion tiene muchas telemetrias
Stations.hasMany(Telemetry, {
  foreignKey: 'StationId',
  as: 'StationTelemetries'
});

// Una telemetria pertenece a una estación
Telemetry.belongsTo(Stations, {
  foreignKey: 'StationId',
  as: 'TelemetryStation'
});
// -------------- RELACIÓN DE TENANT Y SU CONFIGURACION --------------
  
  // Configuration pertenecen a un tenant
  Configuration.belongsTo(User, {
    foreignKey: 'tenantId',
    as: 'tenant',
  });
  User.hasOne(Configuration, {
    foreignKey: 'tenantId',
    as: 'configuration',
  });

  // Wallet pertenecen a un user
  Wallet.belongsTo(User, {
    foreignKey: 'userId',
    as: 'user'
  });
  User.hasOne(Wallet, {
    foreignKey: 'userId',
    as: 'wallet'
  });
  // Wallet pertenecen a un tenant
  Wallet.belongsTo(User, {
    foreignKey: 'tenantId',
    as: 'tenant'
  });
  User.hasMany(Wallet, {
    foreignKey: 'tenantId',
    as: 'walletTenant'
  });

  Rents.belongsTo(User, {
    foreignKey: 'customerId',
    as: 'customer'
  });
  User.hasMany(Rents, {
    foreignKey: 'customerId',
    as: 'rents'
  });

  Rents.belongsTo(Devices, {
    foreignKey: 'deviceId',
    as: 'device'
  });
  Devices.hasMany(Rents, {
    foreignKey: 'deviceId',
    as: 'rents'
  });