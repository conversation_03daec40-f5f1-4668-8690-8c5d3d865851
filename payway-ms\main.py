import json
import uuid

from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException
from modules.bikeSharingApi import add_currency_to_wallet
from modules.paymentdetails import buildpaymentdetails
from modules.payway import (
    CardData,
    PaymentResponse,
    Payway,
    PaywayError,
    TransactionRequieredElements,
)

PRODUCTION=False
PAYWAY_WALLET_AUTH_TOKEN="17814497-a39f-4cc3-819f-4cc8c00d32df"


if(PRODUCTION):
    TOKEN_API_KEY="e1874e139ba34cb6b9dbf44efd98bb2f"
    PAYMENT_API_KEY="c7b69d8b01fd4a16bd82f22543334822"
    ENVIRORMENT_URL="https://ventasonline.payway.com.ar/api/v2"
else:
    # These should come from environment variables or a secure config service
    TOKEN_API_KEY = "V1PpKC5BITNiIL8AyO0ukl8j4naKyA69"
    PAYMENT_API_KEY = "BUsAX2L7OMzga7i7OhDiO50SjevJxRGA"
    ENVIRORMENT_URL="https://developers-ventasonline.payway.com.ar/api/v2"
    
RIDE_PRICE_IN_CENTS = 100000
MICRO_PAYMENT_IN_CENTS = 10000

# --- FastAPI Application ---
app = FastAPI(
    title="Payway Processing API",
    description="API for processing payments via Payway.",
    version="2.0.1",
)


try:
    payway_client = Payway(token_api_key=TOKEN_API_KEY,
                           payment_api_key=PAYMENT_API_KEY,base_url=ENVIRORMENT_URL)
except ValueError as e:
    print(f"CRITICAL: Configuration Error initializing Payway client: {e}")
    payway_client = None


@app.post("/process-tokenized-credit-card", response_model=PaymentResponse, tags=["Payments"])
async def create_tokenized_payment(card_info: CardData):
    """
    Processes a payment using the provided card token by calling the Payway API.
    """

    card_data_for_token = card_info.model_dump()

    payment_amount_in_cents = MICRO_PAYMENT_IN_CENTS

    payment_details_dict = buildpaymentdetails(payment_amount_in_cents=payment_amount_in_cents)

    # 3. Call the actual Payway process_payment method
    payment_response_dict = payway_client.process_payment(
        card_data=card_data_for_token,
        payment_details=payment_details_dict
    )
    
    validated_response = PaymentResponse(**payment_response_dict)
    
    print(payment_response_dict)
    
    transaction_id=validated_response.id
    
    # 4. Perform Refund of payment
    
    payway_client.perform_refund(payment_id=transaction_id)
    
    
    return validated_response
    

@app.post("/payment-form")
async def get_payment_form_hash(card_info: CardData,tags=["Payments"]):
    payment_amount_in_cents = RIDE_PRICE_IN_CENTS

    payment_details_dict = buildpaymentdetails(payment_amount_in_cents=payment_amount_in_cents)
    try:
        # 3. Call the actual Payway process_payment method
        card_data_for_token = card_info.model_dump()
        payment_response_dict = payway_client.build_payment_form(payment_details=payment_details_dict,card_data=card_data_for_token)
        print(f"RESPONSE: {payment_response_dict}")

        print(
            f"--- FastAPI Endpoint: Raw response from Payway ---\n{json.dumps(payment_response_dict, indent=2)}")

        # 4. Validate the response against our Pydantic model
        # This ensures the API response can be parsed correctly by our model.
        # It may fail if the real API response differs from the PaymentResponse model.
        validated_response = PaymentResponse(**payment_response_dict)

        print("--- FastAPI Endpoint: Payment Process Completed Successfully ---")
        return validated_response

    except PaywayError as e:
        # This catches errors raised from the Payway class (e.g., HTTP 4xx/5xx)
        print("--- FastAPI Endpoint: Payment Process FAILED (PaywayError) ---")
        print(f"Error: {e}")
        # Return a 400 Bad Request for payment-related failures.
        raise HTTPException(
            status_code=400,
            detail={"message": "Payment processing failed.",
                    "error_details": str(e)}
        )
    except Exception as e:
        # This catches unexpected errors in the FastAPI endpoint logic itself.
        print("--- FastAPI Endpoint: An unexpected internal error occurred ---")
        print(f"Error: {type(e).__name__} - {e}")
        raise HTTPException(
            status_code=500, detail="An unexpected internal server error occurred.")


@app.post("/process-payment", response_model=PaymentResponse, tags=["Payments"])
async def create_payment(card_info: CardData):
    """
    Processes a payment using the provided card data by calling the Payway API.
    """

    # 1. Prepare card_data dictionary for tokenization
    # The Payway module expects a `fraud_detection` field for tokenization.
    card_data_for_token = card_info.model_dump()
    card_data_for_token['fraud_detection'] = {
        'device_unique_identifier': str(uuid.uuid4())
    }

    # 2. Prepare payment_details dictionary
    payment_amount_in_cents = RIDE_PRICE_IN_CENTS

    payment_details_dict = buildpaymentdetails(payment_amount_in_cents=payment_amount_in_cents)

    print("\n--- FastAPI Endpoint: Calling Payway Service ---")

    try:
        # 3. Call the actual Payway process_payment method
        payment_response_dict = payway_client.process_payment(
            card_data=card_data_for_token,
            payment_details=payment_details_dict
        )

        print(
            f"--- FastAPI Endpoint: Raw response from Payway ---\n{json.dumps(payment_response_dict, indent=2)}")

        # 4. Validate the response against our Pydantic model
        # This ensures the API response can be parsed correctly by our model.
        # It may fail if the real API response differs from the PaymentResponse model.
        validated_response = PaymentResponse(**payment_response_dict)

        print("--- FastAPI Endpoint: Payment Process Completed Successfully ---")
        return validated_response

    except PaywayError as e:
        # This catches errors raised from the Payway class (e.g., HTTP 4xx/5xx)
        print("--- FastAPI Endpoint: Payment Process FAILED (PaywayError) ---")
        print(f"Error: {e}")
        # Return a 400 Bad Request for payment-related failures.
        raise HTTPException(
            status_code=400,
            detail={"message": "Payment processing failed.",
                    "error_details": str(e)}
        )
    except Exception as e:
        # This catches unexpected errors in the FastAPI endpoint logic itself.
        print("--- FastAPI Endpoint: An unexpected internal error occurred ---")
        print(f"Error: {type(e).__name__} - {e}")
        raise HTTPException(
            status_code=500, detail="An unexpected internal server error occurred.")




@app.post("/process-mobile-payment", response_model=PaymentResponse, tags=["Payments"])
async def create_mobile_payment(
    transactionElements: TransactionRequieredElements,
    Authorization: str = Header(None, description="Bearer $token")
):
    """
    Processes a mobile payment using the provided card data by calling the Payway API.
    """
    if not Authorization:
        raise HTTPException(
            status_code=400,
            detail="JWT token is required for authentication."
        )
    
    transactionElementsdump = transactionElements.model_dump()
    
    token_id = transactionElementsdump["token"]
    amount = transactionElementsdump["amount"]
    payment_details_dict = buildpaymentdetails(amount=amount)

    print("\n--- FastAPI Endpoint: Calling Payway Service ---")

    try:
        # 3. Call the actual Payway process_payment method
        payment_response_dict = payway_client.process_mobile_payment(
            payment_details=payment_details_dict,token_id=token_id
        )

        print(f"--- FastAPI Endpoint: Raw response from Payway ---\n{json.dumps(payment_response_dict, indent=2)}")

        # 4. Validate the response against our Pydantic model
        # This ensures the API response can be parsed correctly by our model.
        # It may fail if the real API response differs from the PaymentResponse model.
        validated_response = PaymentResponse(**payment_response_dict)
        
        if(validated_response.status == "approved"):
            ## Add currency to wallet
            response = add_currency_to_wallet(
                api_url="http://bikesharing-server:8080/",
                user_jwt=Authorization,
                amount=amount,
                token=PAYWAY_WALLET_AUTH_TOKEN
            )
            
            if(response.status_code != 200):
                print("--- FastAPI Endpoint: Failed to add credit to the user ---")
                print("Refunding payment due to assignment failure.")
                # Perform a refund if the assignment fails
                payway_client.perform_refund(payment_id=validated_response.id)
                
                raise HTTPException(
                    status_code=response.status_code,
                    detail={"message": "Failed to add credit to the user.",
                            "error_details": response.text}
                )
            
            
            print(f"--- FastAPI Endpoint: Response from bikesharing-ms ---\n{response.text}")
            
            

        return validated_response

    except PaywayError as e:
        # This catches errors raised from the Payway class (e.g., HTTP 4xx/5xx)
        print("--- FastAPI Endpoint: Payment Process FAILED (PaywayError) ---")
        print(f"Error: {e}")
        # Return a 400 Bad Request for payment-related failures.
        raise HTTPException(
            status_code=400,
            detail={"message": "Payment processing failed.",
                    "error_details": str(e)}
        )
    except Exception as e:
        # This catches unexpected errors in the FastAPI endpoint logic itself.
        print("--- FastAPI Endpoint: An unexpected internal error occurred ---")
        print(f"Error: {type(e).__name__} - {e}")
        raise HTTPException(
            status_code=500, detail="An unexpected internal server error occurred.")