import { DataTypes, Sequelize } from 'sequelize';
import sequelize from '../config/database.js';

const Notifications = sequelize.define('Notifications', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  date: { type: DataTypes.DATE, allowNull: false, defaultValue: Sequelize.NOW },
  message: { type: DataTypes.STRING, allowNull: false },
  user: { type: DataTypes.UUID, allowNull: false },
  deviceId: { type: DataTypes.UUID, allowNull: true }
});

export default Notifications;