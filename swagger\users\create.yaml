
paths:
  # -------------> Create User <-------------
  "/users":
    post:
      tags:
        - Users
      summary: Create a new user
      description: "Creates a new user in the system. Password policy: Minimum eight characters, at least one letter and one number"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        200:
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        400:
          description: Bad request
        500:
          description: Internal server error

components:
  schemas:
    User:
      type: object
      required:
        - name
        - surname
        - username
        - password
        - mail
        - tenantId
      properties:        
        name:
          type: string
          description: First name of the user
          example: Facundo
        surname:
          type: string
          description: Last name of the user
          example: Comasco
        username:
          type: string
          description: Unique username
          example: facucomasco
        password:
          type: string
          description: Password of the user
          example: abcdefg
        mail:
          type: string
          description: Email of the user
          example: "<EMAIL>"
        tenantId: 
          type: uuid
          description: Tenant Id
          example: "9964fc0b-0a2c-4eea-9161-3d5a05e5869a"
