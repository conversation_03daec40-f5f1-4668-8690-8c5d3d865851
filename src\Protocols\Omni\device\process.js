import LockStation from "../../../models/lockStations.js";
import Rents from "../../../models/rents.js";
import Station from "../../../models/station.js";
import Telemetry from "../../../models/telemetry.js";

async function processMessages(data, device) {
    try {
        switch (data.commandType) {
            case 'H0':
                await processH0(data, device);
                break;
            case 'R0':
                await processR0(data, device);
                break;
            case 'L0':
                await processL0(data, device);
                break;
            case 'L1':
                await processL1(data, device);
                break;
            case 'Q0', 'SC':
                break;
            default:
                break;

        }
        await addTelemetry(data.payload, device);
        return { code: 200 };
    } catch (error) {
        return { code: 401, error: error };
    }
}

async function processH0(data, device) {
    const payload = data.payload;
    if (device.status == "Rented" && payload.status == "Locked") {
        console.error("Error in device report. Expected status: Unlocked, Received: Locked");
        return;
    }
    if (device.status == "Ready" && payload.status == "Unlocked") {
        console.error("Error in device report. Expected status: Locked, Received: Unlocked");
        return;
    }
}

async function processR0(data, device) {
    if (!device.locking_parameters || device.locking_parameters == {}) {
        return;
    }

    const parameters = device.locking_parameters;
    const payload = data.payload;
    if (payload.UserId != parameters.userid) return { code: 401, error: 'userId doesnt match' };
    if (payload.ts != parameters.timestamp) return { code: 401, error: 'timestamp doesnt match' };
    if (!parameters.lock && payload.Request == "Unlock") {
        parameters.Key = payload.Key;
        device.locking_parameters = parameters;
        device.status = 'Unlocking';
        await device.save();
    }
    else if (parameters.lock && payload.Request == "Lock") {
        parameters.Key = payload.Key;
        device.locking_parameters = parameters;
        device.status = 'Locking';
        data.payload.StationId = null;
        await device.save();
    }
}

async function processL0(data, device) {
    if (!device.locking_parameters || device.locking_parameters == {}) {
        return;
    }

    const parameters = device.locking_parameters;
    const payload = data.payload;
    if (payload.Status == "Success") {
        //CrearRegistroDeRenta
        let lockStation = await LockStation.findByPk(device.LockStationId, {
            include:
                [
                    {
                        model: Station,
                        as: 'relatedStation'
                    }
                ]
        });
        if (!lockStation) {
            console.log("Error: lockStation not found");
            return;
        }
        let station = lockStation.relatedStation;
        if (!station) {
            console.log("Error: Station not found");
            return;
        }

        data.payload.StationId = null;
        data.payload.lockStationId = null;

        station.spots_availables = station.spots_availables + 1;
        device.LockStationId = null;
        device.customerId = parseInt(payload.userId);
        device.status = "Rented";
        device.stationIndex = null;

        await device.save();
        await station.save();

        const rent = {
            deviceId: device.id,
            startTime: Date.now(),
            startStation: station.id,
            customerId: device.customerId,
            serial: device.serial,
            paymentMethod: parameters.paymentMethod,
        };
        await Rents.create(rent);
    }
    else {
        device.customerId = null;
        device.userId = null;
        device.status = "Ready";
        await device.save();
    }
}

async function processL1(data, device) {
    if (!device.locking_parameters || device.locking_parameters == {}) {
        return;
    }

    const parameters = device.locking_parameters;
    const payload = data.payload;
    if (payload.Status == "Success") {
        device.status = "Ready";
        device.locking_parameters = {};
    }
    else {
        device.status = "Rented";
        device.stationId = null;
        device.locking_parameters = {};
    }

    await device.save();
}

async function addTelemetry(payload, device) {
    var telemetry = {}
    var lastTelemetry = await Telemetry.findOne(
        {
            where: { deviceId: device.id },
            order: [['updatedAt', 'DESC']]
        }
    )
    if (lastTelemetry) {
        telemetry = { ...lastTelemetry.dataValues, ...payload };
        delete lastTelemetry.riding_Time;
        delete telemetry.id;
        delete telemetry.createdAt;
        delete telemetry.updatedAt;
    }
    else {
        telemetry = payload;
        telemetry.deviceId = device.id;
    }
    telemetry.ts = Date.now();
    telemetry.userId = device.customerId;

    await Telemetry.create(telemetry).catch(function (err) {
        return { code: 401, error: err };
    });
}

export default { processMessages };
