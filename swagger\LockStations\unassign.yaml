
paths:
  # -------------> Unassign <-------------
  "/lockstations/unassign":
    put:
      tags:
        - Lock Stations
      summary: Unassign lock station from a station.
      description: Unassign lock station from a station.
      parameters:
        - name: stationId
          in: query
          description: Station Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
        - name: lockStationId
          in: query
          description: Lock Station Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"

      responses:
        200:
          description: Lock Station assigned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LockStation'
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error
