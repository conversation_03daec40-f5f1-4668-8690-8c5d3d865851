<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 536.59 79.98">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-1, .cls-2, .cls-3, .cls-4 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: #6a0cf8;
      }

      .cls-3 {
        fill: #fff;
      }

      .cls-4 {
        fill: #309;
      }
    </style>
    <linearGradient id="linear-gradient" x1="0" y1="39.99" x2="153.12" y2="39.99" gradientUnits="userSpaceOnUse">
      <stop offset=".13" stop-color="#6a0cf8"/>
      <stop offset=".56" stop-color="#fd16b8"/>
      <stop offset=".97" stop-color="#fdba7f"/>
    </linearGradient>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <g>
        <path class="cls-3" d="m408.48,25.24h5.42v4.86c3.63-4.94,8.35-6.49,13.82-5.93,5.49.56,9.65,3.38,12.41,7.92,1.69-1.67,3.19-3.46,4.98-4.88,7.48-5.93,23.64-4.35,26.62,10.68.31,1.58.48,3.21.49,4.83.05,8.32.02,16.65.02,24.97,0,.22-.03.43-.05.75h-5.5v-1.5c0-7.67.08-15.33-.05-23-.04-2.42-.33-4.92-1.03-7.23-1.48-4.89-5.44-7.56-10.61-7.59-5.18-.04-9.15,2.58-10.78,7.39-.63,1.88-1.05,3.92-1.08,5.9-.12,8.15-.06,16.29-.07,24.44,0,.52,0,1.03,0,1.62h-5.54c0-.48,0-.98,0-1.48,0-7.75.1-15.51-.06-23.26-.05-2.54-.44-5.18-1.26-7.57-1.63-4.75-5.6-7.13-10.85-7.04-4.98.09-8.85,2.72-10.33,7.43-.69,2.18-1.03,4.54-1.07,6.82-.13,7.84-.05,15.68-.05,23.52,0,.48,0,.96,0,1.55h-5.44V25.24Z"/>
        <path class="cls-3" d="m392.39,32.68V9.98h5.45v58.45h-5.43v-7.68c-4.96,6.61-11.33,9.32-19.06,8.73-6.29-.48-11.62-3.08-15.82-7.84-7.79-8.81-7.11-22.82,1.4-31.07,8.06-7.81,24.22-10.01,33.46,2.11Zm.28,14.33c.06-9.89-7.62-17.85-17.3-17.92-10.08-.07-17.7,7.57-17.74,17.81-.04,10.13,7.53,17.64,17.84,17.68,9.44.04,17.14-7.84,17.2-17.57Z"/>
        <path class="cls-3" d="m338.47,32.97v-7.72h5.43v43.22h-5.36v-7.29c-4.6,5.71-10.48,8.54-17.71,8.34-7.15-.19-13.24-2.9-17.72-8.57-7.42-9.39-6.39-22.88,2.23-30.7,4.88-4.42,10.66-6.43,17.21-6.16,6.6.28,11.88,3.11,15.93,8.87Zm.29,14c.03-9.86-7.72-17.84-17.35-17.88-10.06-.04-17.66,7.65-17.68,17.88-.02,10.04,7.53,17.58,17.61,17.61,9.66.03,17.39-7.79,17.42-17.61Z"/>
        <path class="cls-3" d="m498.6,25.26h5.38v5.36c.34-.27.52-.36.63-.5,4-5.11,9.42-6.58,15.57-5.9,10.47,1.16,15.94,8.99,16.24,18.18.28,8.45.11,16.91.14,25.36,0,.21-.05.42-.08.69h-5.47c0-.51,0-1.01,0-1.52-.02-8.02.1-16.04-.11-24.05-.14-5.34-2.24-9.85-7.25-12.39-8.15-4.14-17.55.61-19.23,9.68-.28,1.5-.35,3.05-.36,4.57-.04,7.4-.02,14.81-.02,22.21v1.49h-5.44V25.26Z"/>
        <path class="cls-3" d="m482.77,68.46V25.24h5.41v43.22h-5.41Z"/>
        <path class="cls-3" d="m482.78,19.81v-9.82h5.39v9.82h-5.39Z"/>
      </g>
      <g>
        <path class="cls-3" d="m262.8,30.14v-5.42h9.55v43.71h-9.51v-5.26c-.54.51-.82.77-1.11,1.04-6.61,6.28-16.71,7.62-24.58,3.09-6.22-3.59-9.53-9.26-10.71-16.2-1.31-7.7.27-14.74,5.48-20.72,6.62-7.59,18.71-10.12,27.93-2.96.98.76,1.84,1.69,2.94,2.72Zm-12.06,30.06c3.83-.09,7.53-2.28,9.92-6.59,1.64-2.97,2.04-6.18,1.62-9.54-1.01-8.05-8.93-13.25-16.6-10.67-5.62,1.89-8.36,6.19-9.05,11.92-.96,7.87,5.19,14.89,14.12,14.88Z"/>
        <path class="cls-3" d="m186.66,10.02h10.4v34.97l.26.14c.26-.35.54-.68.79-1.04,4.16-6.12,8.33-12.23,12.45-18.38.53-.79,1.08-1.07,2.02-1.05,3.5.06,7.01.02,10.82.02-5.35,7.28-10.57,14.37-15.83,21.52,5.92,7.41,11.82,14.79,17.89,22.38-2.67,0-5.05.01-7.44,0-1.96-.01-4.66,0-5.8-.27-.23-.06-2.37-2.93-3.5-4.49-3.52-4.86-7.04-9.72-10.56-14.57-.25-.35-.52-.69-1.01-1.34v20.61h-10.48V10.02Z"/>
        <path class="cls-3" d="m280.5,24.73h10.36v43.74h-10.36V24.73Z"/>
        <path class="cls-3" d="m290.85,19.57h-10.38v-9.54h10.38v9.54Z"/>
      </g>
    </g>
    <g>
      <path class="cls-2" d="m124.83,79.98c-10.68,0-20.73-4.16-28.28-11.72l-14.14-14.14-14.14,14.14c-7.56,7.55-17.6,11.72-28.28,11.72s-20.73-4.16-28.28-11.72C-3.9,52.67-3.9,27.29,11.7,11.7c15.6-15.6,40.97-15.6,56.57,0l14.14,14.14,14.14-14.14c15.6-15.59,40.97-15.6,56.57,0l-14.14,14.14c-7.8-7.8-20.49-7.8-28.28,0l-14.14,14.14,14.14,14.14c3.78,3.78,8.8,5.86,14.14,5.86s10.36-2.08,14.14-5.86l14.14,14.14c-7.56,7.55-17.6,11.72-28.28,11.72ZM39.98,19.99c-5.12,0-10.24,1.95-14.14,5.85-7.8,7.8-7.8,20.49,0,28.28,7.8,7.8,20.49,7.8,28.28,0l14.14-14.14-14.14-14.14c-3.9-3.9-9.02-5.85-14.14-5.85Z"/>
      <path class="cls-1" d="m11.7,11.7C3.9,19.49,0,29.74,0,39.98h19.99c0-5.12,1.95-10.24,5.85-14.14,3.9-3.9,9.02-5.85,14.14-5.85s10.24,1.95,14.14,5.85l14.14,14.14,14.14,14.14,14.14,14.14c7.56,7.55,17.6,11.72,28.28,11.72s20.73-4.16,28.28-11.72l-14.14-14.14c-3.78,3.78-8.8,5.86-14.14,5.86s-10.36-2.08-14.14-5.86l-14.14-14.14h0s-14.14-14.14-14.14-14.14l-14.14-14.14c-15.6-15.59-40.97-15.6-56.57,0Z"/>
      <rect class="cls-4" x="72.41" y="29.98" width="20" height="20" transform="translate(-4.13 69.98) rotate(-45)"/>
    </g>
  </g>
</svg>