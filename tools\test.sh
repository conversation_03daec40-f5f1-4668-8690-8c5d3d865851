#!/bin/bash

# This script performs a login request to get an access token,
# then uses that token to make an authenticated request to another endpoint.

# --- Configuration ---
# Base URL for the API
BASE_URL="http://localhost:8080"

# Credentials for the login request
USERNAME="<EMAIL>"
PASSWORD="customer"

echo "--- Step 1: Logging in to get the access token ---"

# Perform the login request and store the full response.
# The -s flag makes curl silent (no progress meter).
# We use jq to extract the 'accessToken' field from the JSON response.
# The -r flag for jq outputs the raw string without quotes.
ACCESS_TOKEN=$(curl -s -X 'POST' \
  "${BASE_URL}/users/login" \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d "{
  \"username\": \"${USERNAME}\",
  \"password\": \"${PASSWORD}\"
}" | jq -r '.accessToken')

# Check if the ACCESS_TOKEN was successfully extracted.
if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" == "null" ]; then
  echo "Error: Could not retrieve access token. Please check your credentials or the server."
  exit 1
fi

echo "Login successful. Token received."
# For debugging, you can uncomment the next line to see the token
# echo "Access Token: $ACCESS_TOKEN"
echo "" # Add a newline for better readability

echo "--- Step 2: Accessing the wallet with the token ---"

# Use the extracted ACCESS_TOKEN to make an authenticated GET request.
# The token is passed in the 'Authorization: Bearer' header.
curl -X 'GET' \
  "${BASE_URL}/users/wallet" \
  -H 'accept: */*' \
  -H "Authorization: Bearer ${ACCESS_TOKEN}"

# Add a final newline for clean output in the terminal
echo ""
