
paths:
  # -------------> get station with relations <-------------
  "/stations/relations":
    get:
      tags:
        - Stations
      summary: Get a station with relations.
      description: Get a station with Lock Stations details and lock station model assigned.
      parameters:
        - name: id
          in: query
          description: Station Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
      responses:
        200:
          description: Station
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Station'
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error