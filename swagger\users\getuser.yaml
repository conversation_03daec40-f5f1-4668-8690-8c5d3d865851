
paths:
  # -------------> Get User <-------------
  "/users":
    get:
      security:
        - bearerAuth: []
      tags:
        - Users
      summary: Get user info
      description: Gets all data from user.
      parameters:
        - name: id
          in: query
          description: User Id
          required: true
          schema:
            type: integer
            example: 1
      responses:
        200:
          description: User data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error

components:
  schemas:
    User:
      type: object
      required:
        - name
        - surname
        - username
        - password
        - mail
      properties:        
        name:
          type: string
          description: First name of the user
          example: Facundo
        surname:
          type: string
          description: Last name of the user
          example: Comasco
        username:
          type: string
          description: Unique username
          example: facucomasco
        password:
          type: string
          description: Password of the user
          example: abcdefg
        mail:
          type: string
          description: Email of the user
          example: "<EMAIL>"
