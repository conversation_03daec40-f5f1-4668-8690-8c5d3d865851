
paths:
  # -------------> get station devices <-------------
  "/stations/devices":
    get:
      tags:
        - Stations
      summary: Get station devices.
      description: Get the station devices.
      parameters:
        - name: id
          in: query
          description: Station Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
      responses:
        200:
          description: Station devices
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Device'
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error