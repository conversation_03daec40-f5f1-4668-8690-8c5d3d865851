components:
  schemas:
    Device:
      type: object
      required:
        - serial
        - phone
        - type
        - modelId
        - qrCode
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        tenantId:
          type: integer
          description: Tenant identifier associated with this device model
          example: 2
          readOnly: true
        serial:
          type: string
          description: Serial number of the device
          example: "SN-12345"
        phone:
          type: string
          description: Phone number used for communication and tracking
          example: "1123456789"
        modelId:
          type: string
          format: uuid
          description: Device model
          example: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        type:
          type: string
          enum: [Bike20, Bike26, Bike29, Scooter]
          description: Type of device
          example: Bike29
        description:
          type: string
          description: Optional description of the device
          example: Red scooter
        qrCode:
          type: string
          description: QR code of the device
          example: "SN-12345"