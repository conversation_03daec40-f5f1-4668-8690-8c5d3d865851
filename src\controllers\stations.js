import axios from 'axios';
import sequelize from '../config/database.js';
import authenticate from '../middlewares/authenticate.js';
import Device from '../models/device.js';
import LockStation from '../models/lockStations.js';
import LockStationsModel from '../models/lockStationsModels.js';
import stationModel from '../models/station.js';

const newStation = async (req, res) => {
    try {
        const user = req.user;
        const new_station = req.body;

        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to create stations" });
        if (!new_station.lat) return res.status(400).send({ error: "No latitude provided" });
        if (!new_station.lon) return res.status(400).send({ error: "No longitude provided" });
        if (!new_station.protocol) return res.status(400).send({ error: "No protocol provided" });

        if (!new_station.address) {
            let url = 'https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat=' + new_station.lat + '&lon=' + new_station.lon;

            try {
                var response = await axios.get(url, {
                  headers: {
                    'User-Agent': 'Mozilla/5.0',
                    'Accept': 'application/json',
                  }
                });
                var data = response?.data;
                console.log(data);
                var address = data?.address;
                if (null == address?.road) {
                    new_station.address = data?.display_name;
                    return;
                }
                new_station.address = address.road;
                if (address.house_number) {
                    new_station.address += " " + address.house_number;
                }
            } catch (error) {
                console.error(error);
                return res.status(500).json({ error: error });
            }
        }
        new_station.tenantId = user.id;
        new_station.spots = 0;
        new_station.spots_availables = 0;
        const newStation = await stationModel.create(new_station);
        return res.status(200).json(newStation);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getStation = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (!id) return res.status(400).send({ error: "id not provided" });

        var station = await stationModel.findByPk(id);
        if (!station) return res.status(404).send({ error: "Station not found" });

        if (user.type == "Customer") {
            if (station.tenantId != user.tenantId) return res.status(400).send({ error: "Station read not permitted" });
        }
        else if (user.type == 'Tenant') {
            if (station.tenantId != user.id) return res.status(400).send({ error: "Station read not permitted" });
        }

        return res.status(200).json(station);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getStationDevices = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (!id) return res.status(400).send({ error: "id not provided" });
        
        var station = await stationModel.findByPk(id, {
            include:
            {
                model: LockStation,
                as: 'relatedLockStations',
                include: {
                    model: Device,
                    as: 'relatedDevices'
                }
            }
        });

        if (!station) return res.status(404).send({ error: "Station not found" });

        if (user.type == "Customer") {
            if (station.tenantId != user.tenantId) return res.status(400).send({ error: "Station read not permitted" });
        }
        else if (user.type == 'Tenant') {
            if (station.tenantId != user.id) return res.status(400).send({ error: "Station read not permitted" });
        }

        const devices = station.relatedLockStations.flatMap(lockStation => lockStation.relatedDevices);

        return res.status(200).json(devices);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getStationDevicesWithAutonomy = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (!id) return res.status(400).send({ error: "id not provided" });
        
        var station = await stationModel.findByPk(id, {
            include:
            {
                model: LockStation,
                as: 'relatedLockStations',
                include: {
                    model: Device,
                    as: 'relatedDevices'
                }
            }
        });

        if (!station) return res.status(404).send({ error: "Station not found" });

        if (user.type == "Customer") {
            if (station.tenantId != user.tenantId) return res.status(400).send({ error: "Station read not permitted" });
        }
        else if (user.type == 'Tenant') {
            if (station.tenantId != user.id) return res.status(400).send({ error: "Station read not permitted" });
        }

        const devices = station.relatedLockStations.flatMap(lockStation => lockStation.relatedDevices);

        if (devices.length === 0) {
            return res.status(200).json([]);
        }

        // Search all the autonomies at one, to avoid multiple database requests
        // It is more efficient than making each request in the loop
        const deviceIds = devices.map(device => device.id);
        const latestAutonomies = await sequelize.query(`
            SELECT t."deviceId", t."battery_level"
            FROM "Telemetries" t
            INNER JOIN (
                SELECT "deviceId", MAX("createdAt") AS maxCreatedAt
                FROM "Telemetries"
                WHERE "deviceId" IN (:deviceIds)
                GROUP BY "deviceId"
            ) latest
            ON t."deviceId" = latest."deviceId" AND t."createdAt" = latest.maxCreatedAt
            `, {
            replacements: { deviceIds },
            type: sequelize.QueryTypes.SELECT
        });

        const autonomyMap = new Map();
        for (const autonomy of latestAutonomies) {
            autonomyMap.set(autonomy.deviceId, autonomy);
        }

        // TODO: Get the efficiency
        const deviceBatteryEfficiency = 0.3;

        for (const device of devices) {
            const autonomy = autonomyMap.get(device.id);
            device.dataValues.autonomy = autonomy
                ? {
                    batteryLevel: autonomy.battery_level,
                    kilometers: autonomy.battery_level * deviceBatteryEfficiency
                }
                : null;
        }

        return res.status(200).json(devices);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getstationswithrelations = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (!id) return res.status(400).send({ error: "id not provided" });

        var station = await stationModel.findByPk(id, {
            include:
            {
                model: LockStation,
                as: 'relatedLockStations',
                include: {
                    model: LockStationsModel,
                    as: 'relatedModel',
                }
            }
        });


        if (!station) return res.status(404).send({ error: "Station not found" });

        if (user.type == "Customer") {
            if (station.tenantId != user.tenantId) return res.status(400).send({ error: "Station read not permitted" });
        }
        else if (user.type == 'Tenant') {
            if (station.tenantId != user.id) return res.status(400).send({ error: "Station read not permitted" });
        }

        return res.status(200).json(station);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getAllStations = async (req, res) => {
    try {
        const user = req.user;

        var stations;
        if (authenticate.hasAuthority("Tenant", user)) {
            stations = await stationModel.findAll({
                where: {
                    tenantId: user.id,
                },
            });
        }
        else if (authenticate.hasAuthority("Customer", user)) {
            stations = await stationModel.findAll({
                where: {
                    tenantId: user.tenantId,
                },
            });
        }
        else if (authenticate.hasAuthority("Sysadmin", user)) {
            stations = await stationModel.findAll();
        }

        return res.status(200).json(stations);
    } catch (error) {
        res.status(500).json({ error: error });
    }
}

const updateStation = async (req, res) => {
    try {
        const user = req.user;
        const update_station = req.body;

        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to update stations" });
        if (!update_station.id) return res.status(400).send({ error: "Station id not provided" });

        const Station = await stationModel.findByPk(update_station.id);
        if (!Station) return res.status(404).send({ error: "Station not found" });
        if (Station.tenantId != user.id) return res.status(400).send({ error: "You are not the owner of this Station" });

        if (update_station.address) {
            Station.address = update_station.address;
        }
        if (update_station.lat) {
            Station.lat = update_station.lat;
        }
        if (update_station.lon) {
            Station.lon = update_station.lon;
        }
        if (update_station.protocol) {
            Station.protocol = update_station.protocol;
        }
        if (update_station.name) {
            Station.name = update_station.name;
        }
        if (update_station.description) {
            Station.description = update_station.description;
        }
        await Station.save();
        return res.status(200).json(Station);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const deleteStation = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to delete stations" });
        if (!id) return res.status(400).send({ error: "id not provided" });

        var station = await stationModel.findByPk(id);
        if (!station) return res.status(404).send({ error: "Station not found" });
        if (station.tenantId != user.id) return res.status(400).send({ error: "Station delete not permitted" });
        await station.destroy();
        return res.status(200).send();
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

export default { newStation, updateStation, deleteStation, getStation, getStationDevices,
    getStationDevicesWithAutonomy, getAllStations, getstationswithrelations };