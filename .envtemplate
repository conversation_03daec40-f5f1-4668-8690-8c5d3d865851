# DATABASE CONFIGURATION
POSTGRESDB_NAME=BikeSharing
POSTGRESDB_USER=BikeSharing
POSTGRESDB_ROOT_PASSWORD=BikeSharing
POSTGRESDB_HOST=postgresdb
POSTGRESDB_DIALECT=postgres
POSTGRESDB_LOCAL_PORT=5432
POSTGRESDB_DOCKER_PORT=5432

# PGADMIN FOR EXPLORING DATABASE
PGADMIN_LOCAL_PORT=9999
PGADMIN_DOCKER_PORT=8080
PGADMIN_DEFAULT_PSW=123456
PGADMIN_DEFAULT_EMAIL=<EMAIL>

# TOKEN DE JWT
TOKENSECRET=tokenSecret
REFRESHSECRET=refreshSecret

# SERVER PORT
PORT=8080

# INITIAL USERS
SYSADMINNAME=Facundo
SYSADMINSURNAME=Comasco
SYSADMINUSERNAME=<EMAIL>
SYSADMINMAIL=<EMAIL>
SYSADMINPASSWORD=123456

TENANTNAME=DON
TENANTSURNAME=Mirgor
TENANTUSERNAME=<EMAIL>
TENANTMAIL=<EMAIL>
TENANTPASSWORD=tenant

TENANT2NAME=Facundo
TENANT2SURNAME=Comasco
TENANT2USERNAME=<EMAIL>
TENANT2MAIL=<EMAIL>
TENANT2PASSWORD=facundo

CUSTOMERNAME=Customer
CUSTOMERSURNAME=Mirgor
CUSTOMERUSERNAME=<EMAIL>
CUSTOMERMAIL=<EMAIL>
CUSTOMERPASSWORD=customer

#SERVER VERSION
SERVER_VERSION=10

# OAUTH GOOGLE
GOOGLE_CLIENT_ID=completar
GOOGLE_CLIENT_SECRET=completar
GOOGLE_REDIRECT_URI=http://localhost:8080/users/google/callback


# Protocols

OMNI_PORT=8085
OMNI_PORT_DEBUG=8086
OMNI_PORT_LOCK_STATION=8087

DEBUGRESPONSES=false

#Metodos de pago
# FIXED, TIME, WALLETFIX, WALLETTIME
# FIXED: El alquiler tiene costo fijo sin importar el tiempo que tengas la bicicleta.
#        Se paga por "el viaje"
# TIME: El alquiler se paga por el tiempo que se tiene la bici.
#       Debe tener una tarjeta almacenada para generarle el pago
# KM: Paga por distancia recorrida
#     Mas "justo" para la gente que hace trayectos cortos, y se monetiza mejor los trayectos largos
# WALLETFIX: Se pueden comprar viajes por anticipado y tenerlos en la app. 
#            Te da la posibilidad de comprar todos los viajes del mes al principio cuando cobras
# WALLETTIME: Se carga credito de "tiempo" en la app, y lo podes ir consumiendo de ahi
#             No hay necesidad de hacer pagos cada vez que vas a llevarte una bici, te descuenta credito de app

PAYMENTMETHOD= FIXED
MERCADO_PAGO_SAMPLE_PUBLIC_KEY=completar
MERCADO_PAGO_SAMPLE_ACCESS_TOKEN=completar

# Payment microservice auth tokens
PAYWAY_WALLET_AUTH_TOKEN=17814497-a39f-4cc3-819f-4cc8c00d32df
MERCADOPAGO_WALLET_AUTH_TOKEN=73cc2939-c72e-41e7-91a1-fea5ff16f94d