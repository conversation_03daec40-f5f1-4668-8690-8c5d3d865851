
class IoTResponses {
    constructor() {
        this.END_DELIMITER = '#';
        this.FIELD_SEPARATOR = ',';
        this.COMMAND_HEADER_SERVER_TO_IOT = '*SCOS';
        this.COMMAND_HEADER_IOT_TO_SERVER = '*SCOR';
    }

    async response(data, device) {
        let _responseMessage;
        switch (data.commandType) {
            case 'Q0': // Check in command
                _responseMessage = this._Command_Q0();
                break;
            case 'H0': // Heartbeat command
                _responseMessage = await this._Command_H0();
                break;
            case 'SD': // Get charging station status command
                _responseMessage = await this._Command_SD();
                break;
            default:
                // parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
        }
        return _responseMessage;
    }

    ///////////////////////////    
    //SERVER to IOT RESPONSES//
    ///////////////////////////


    /**
     * Check-in response - Q0
     *   IoT automatically sends this command as the first transmission upon
     *   every connection to the server (including reconnection after
     *   disconnection). No response is needed
     */
    _Command_Q0() {
        try {
            //No response
            return;
        } catch (error) {
            return {};
        }
    }

    /**
     * Heartbeat response - H0
     *   The lock transmits this command at regular intervals (default: every 4
     *   minutes) to maintain the connection with the server.
     */
    async _Command_H0() {
        try {
            //No response
            return;
        } catch (error) {
            return {};
        }
    }
    
    /**
     * Get charging station status response - SD
     *   Charging status received every time a device in a lock station changes.
     */
    async _Command_SD() {
        try {
            //No response
            return;
        } catch (error) {
            return {};
        }
    }
    
    buildMessage(imei, commandType, payloadFields) {
        const payloadString = [this.COMMAND_HEADER_SERVER_TO_IOT, 'OM', imei, commandType, ...payloadFields].join(this.FIELD_SEPARATOR);
        return payloadString + this.END_DELIMITER + '\n'; // Add the newline at the end
    }
}

export default IoTResponses;