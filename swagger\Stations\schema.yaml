components:
  schemas:
    Station:
      type: object
      required:
        - name
        - lat
        - lon
        - serial
        - spots
        - spots_availables
        - protocol
      properties:
        id:
          type: string
          description: id
          example: "ad763a26-00ec-4c43-9611-7b4b978990eb"
          readOnly: true
        tenantId:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          description: Station name
          example: Corrientes
        lat:
          type: double
          description: Station latitude
          example: -34.555039
        lon:
          type: double
          description: Station longitude
          example: -58.445939
        address:
          type: string
          description: Station Address
          example: Calle Corrientes 1100, Caba, Buenos Aires, Argentina
        serial:
          type: string
          description: Station serial number
          example: AF324GAC0E123
        spots: 
          type: integer
          description: Number of locks stations
          example: 6
          readOnly: true
        spots_availables: 
          type: integer
          description: Number of stations availables (with no bike)
          example: 3
          readOnly: true
        description:
          type: string
          description: Station description
          example: Corrientes
        protocol:
          type: string
          description: Station protocol
          example: OMNI