import fetch from 'node-fetch';
import colors from '../colors.js';
import inputs from '../inputs.js';

export default async function createStation(jwt, istenant) {
  let startTimestamp;
  let finishTimestamp;
  console.log();
  console.log(colors.bg.black + colors.fg.green + "#############################################" + colors.reset);
  console.log(colors.bg.black + colors.fg.green + 'START TEST => CREATE STATION' + colors.reset);
  console.log(colors.bg.black + colors.fg.green + "#############################################" + colors.reset);
  console.log();

  const endpoint = "/stations";
  const method = "POST";

  let Request = {
    "name": inputs.STATIONS.name,
    "lat": inputs.STATIONS.lat,
    "lon": inputs.STATIONS.lon,
    "address": inputs.STATIONS.address,
    "protocol": inputs.STATIONS.protocol,
    "serial": inputs.STATIONS.serial,
    "description": inputs.STATIONS.description
  };
  startTimestamp = Date.now();
  try {
    const response = await fetch('http://127.0.0.1:8080' + endpoint, {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwt}`
      },
      body: JSON.stringify(Request)
    });
    const resBody = await response.text();
    if (process.env.DEBUGRESPONSES === 'true') {
      console.log('Backend_Response:');
      console.log(response.status + " : " + response.statusText + " - " + resBody)
      console.log();
    }
    finishTimestamp = Date.now();
    const TotalTime = finishTimestamp - startTimestamp;
    console.log('Response time: ' + TotalTime + " ms");

    if (istenant) {
      if (response.status != 200) return false;
      else return JSON.parse(resBody).id;
    }
    else {
      if (response.status == 200) return false;
      else return true;
    }

  } catch (error) {
    console.error('Error:', error);
    return false;
  }
}



