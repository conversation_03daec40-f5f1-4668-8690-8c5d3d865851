import EventEmitter from 'events';
import net from 'net';

class TCPServer extends EventEmitter {
    constructor(port, host = '0.0.0.0') {
        super();
        this.port = port;
        this.host = host;
        this.server = null;
        this.connections = new Set();
    }

    start() {
        return new Promise((resolve, reject) => {
            this.server = net.createServer((socket) => {
                this.connections.add(socket);
                this.emit('connection', socket);

                socket.on('data', (data) => {
                    // console.log(Date.now() + " - recv: " + data.toString().trim());
                    this.emit('data', socket, data);
                });

                socket.on('end', () => {
                    this.connections.delete(socket);
                    this.emit('disconnect', socket);
                });

                socket.on('error', (err) => {
                    this.emit('error', socket, err);
                });
            });

            this.server.listen(this.port, this.host, () => {
                console.log("Servidor TCP escuchando en <span class='math-inline'>" + this.host + ":" + this.port);
                this.emit('ready');
                resolve();
            });

            this.server.on('error', (err) => {
                this.emit('serverError', err);
                reject(err);
            });

            this.server.on('close', () => {
                this.emit('close');
                console.log('Servidor TCP cerrado.');
            });
        });
    }

    stop() {
        return new Promise((resolve, reject) => {
            if (this.server && this.server.listening) {
                this.server.close((err) => {
                    if (err) {
                        return reject(err);
                    }
                    this.connections.forEach(socket => socket.destroy());
                    this.connections.clear();
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }

    send(socket, data) {
        // console.log(Date.now() + " - send: " + data.toString().trim());
        if (socket && !socket.destroyed) {
            socket.write(data);
        } else {
            console.warn('Intento de enviar datos a un socket no válido o destruido.');
        }
    }

    broadcast(data) {
        this.connections.forEach(socket => {
            this.send(socket, data);
        });
    }
}

export default TCPServer;