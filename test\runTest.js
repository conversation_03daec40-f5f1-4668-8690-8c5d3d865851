import colors from "./colors.js";
import createStation from "./stations/create.js";
import deleteStation from "./stations/delete.js";
import getStation from "./stations/get.js";
import getAllStations from "./stations/getall.js";
import changePassword from "./users/changePassword.js";
import createuser from "./users/create.js"
import createUserInvalidTenant from "./users/createwithinvalidtenant.js";
import createUserSameMail from "./users/createwithsamemail.js";
import createUserSameUserName from "./users/createwithsameusername.js";
import deleteUser from "./users/delete.js";
import loginUser from "./users/login.js";
import loginTenant from "./users/loginTenant.js";

let totalTests=0;
let successfulTests=0;
let jwt;
let tenant_jwt;
let stationId;
let startTimestamp;
let finishTimestamp;

export default async function   runTest() {
    startTimestamp = Date.now();
    for (const test of tests) {
        if(test.exc){
            totalTests++;
            try {
                const result = await test.func();
                if (result) {
                    successfulTests++;
                    console.log(colors.bg.black + colors.fg.green+ `${test.description}: Success` + colors.reset);
                } else {
                    console.log(colors.bg.black + colors.fg.red+ `${test.description}: Failed` + colors.reset);
                }
            } catch (error) {
                console.log(`${test.description}: Error`, error);
            }
            console.log(`Total tests: ${totalTests}`);
            console.log(`Successful tests: ${successfulTests}`);
        }
    }
    finishTimestamp = Date.now();
    const TotalTime = finishTimestamp - startTimestamp;

    console.log(colors.bg.white + colors.fg.magenta+ `Total tests: ${totalTests}`+colors.reset);
    console.log(colors.bg.white + colors.fg.magenta+`Successful tests: ${successfulTests}`+colors.reset);
    console.log(colors.bg.white + colors.fg.magenta+'Test time: ' + TotalTime + " ms"+colors.reset);
}
const tests = [
    {
        description: 'Create User',
        func: async () =>await createuser() ,
        exc: true        
    },
    {
        description: 'Create User with invalid tenant',
        func: async () => await createUserInvalidTenant(),
        exc: true        
    },
    {
        description: 'Create User with same mail',
        func: async () => await createUserSameMail(),
        exc: true        
    },
    {
        description: 'Create User with same username',
        func: async () => await createUserSameUserName(),
        exc: true        
    },
    {
        description: 'Login Tenant',
        func: async () => {
            tenant_jwt = await loginTenant();
            return tenant_jwt;
        },
        exc: true        
    },
    {
        description: 'Login User',
        func: async () => {
            jwt = await loginUser();
            return jwt;
        },
        exc: true        
    },
    {
        description: 'Change password',
        func: async () =>  await changePassword(jwt),
        exc: true        
    },
    {
        description: 'Create station (Customer)',
        func: async () => await createStation(jwt,false),
        exc: true        
    },
    {
        description: 'Create station (Tenant)',
        func: async () => {
            stationId = await createStation(tenant_jwt, true);
            return stationId;
        },
        exc: true        
    },
    {
        description: 'Get station (Customer)',
        func: async () => await getStation(jwt,stationId,false),
        exc: true        
    },
    {
        description: 'Get station (Tenant)',
        func: async () => await getStation(tenant_jwt,stationId,false),
        exc: true        
    },
    {
        description: 'Get all stations (Customer)',
        func: async () => await getAllStations(jwt,false),
        exc: true        
    },
    {
        description: 'Get all stations (Tenant)',
        func: async () => await getAllStations(tenant_jwt,false),
        exc: true        
    },
    {
        description: 'Delete station (Customer)',
        func: async () => await deleteStation(jwt,stationId,false),
        exc: true        
    },
    {
        description: 'Delete station (Tenant)',
        func: async () => await deleteStation(tenant_jwt,stationId, true),
        exc: true        
    },
    {
        description: 'Delete User',
        func: async () =>await deleteUser(jwt) ,
        exc: true        
    },
];
