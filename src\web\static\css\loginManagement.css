.login{
    /* background-image: url("../img/background.jpeg"); */
    /* background-size: cover; */

    background: linear-gradient(-45deg,
    rgb(39, 168, 243),
    rgb(252, 252, 233),
    rgb(39, 168, 243)
  );        /* repeating-linear-gradient( 72deg, #fb9716, #6a3302 400px, #683100 100px, #ff8f00 2000px ) */
        background-size: 200% 200%;
       animation: gradient 30s ease infinite;
     
      
      
    height: 100vh;
    width: 100vw;
}
@keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
.lang_button{
    position: absolute;
    right: 10px;
    width: 70px;
    height: 55px;
    outline: none;
    background-repeat: no-repeat;
    background-size: cover;
    background-color: transparent; 
    border: none;
}
.login_container{
    display: block;
    align-content: center;
    height: 100vh;
    text-align: center;
}
.login_form{
    display: inline-block;
  text-align: center;
  width: 540px;
  border-radius: 24px;
  background-color: rgba(0, 0, 0, 0.7);
  height: 550px;
  background: rgba(0,0,0,0.7);
    /* background-color: rgba(0, 0, 0, 0.5); */
}
h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    letter-spacing: 1px;
    margin-bottom: 1rem;
    text-align: center;
  }
  h3::after {
    content: '';
    display: block;
    width: 90%;
    height: 1px;
    background-color: #ffffff;
    margin: 12px auto 0 auto;
    border-radius: 2px;
  }
  h6 {
    font-family: 'Poppins', sans-serif;
    letter-spacing: 1px;
    margin-top: 10px;
    text-align: center;
  }
  h6::after {
    content: '';
    display: block;
    width: 90%;
    height: 1px;
    background-color: #ffffff;
    margin: 12px auto 0 auto;
    border-radius: 2px;
  }
.login input{
    height: 45px;
    width: 80%;
    margin-bottom: 10px;
    border: 2px solid white;
    border-radius: 5px;
    font-size: 22px;
    text-align: center;
  background: white;
  color: rgb(110,110,110);
}
.login button {
    height: 50px;
    width: 60%;
    background: linear-gradient(to right, #007bff, #00c6ff);
    margin-top: 40px;
    color: white;
    font-size: 22px;
    font-weight: 600;
    /* font-family: 'Segoe UI', 'Roboto', sans-serif; */
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
    cursor: pointer;
    transition: background 0.3s ease, box-shadow 0.3s ease;
    user-select: none;
  }
  
  .login button:hover {
    background: linear-gradient(to right, #0056b3, #0099dd);
    box-shadow: 0 6px 16px rgba(0, 86, 179, 0.6);
  }
@media (min-width: 768px) {

}