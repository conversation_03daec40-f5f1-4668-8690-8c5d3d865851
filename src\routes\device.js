import express from 'express';
import deviceController from '../controllers/device.js';
import authenticate from '../middlewares/authenticate.js';

const router = express.Router();

router.post('/', authenticate.authenticateToken, deviceController.newDevice);
router.get('/', authenticate.authenticateToken, deviceController.getDevice);
router.get('/bySerial', authenticate.authenticateToken, deviceController.getDeviceBySerial);
router.get('/byQrCode', authenticate.authenticateToken, deviceController.getDeviceByQrCode);
router.get('/all', authenticate.authenticateToken, deviceController.getAllDevices);
router.get('/position', authenticate.authenticateToken, deviceController.getPositionAllDevices);
router.get('/all/withAutonomy', authenticate.authenticateToken, deviceController.getAllDevicesWithAutonomy);
router.get('/autonomy', authenticate.authenticateToken, deviceController.getDeviceAutonomy);
router.put('/', authenticate.authenticateToken, deviceController.updateDevice);
router.delete('/', authenticate.authenticateToken, deviceController.deleteDevice);

router.put('/assign', authenticate.authenticateToken, deviceController.assingDevice);
router.put('/unassign', authenticate.authenticateToken, deviceController.unassignDevice);

export default router;