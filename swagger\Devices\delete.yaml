
paths:
  # -------------> Delete Device by ID <-------------
  "/devices":
    delete:
      tags:
        - Devices
      summary: Delete a device by ID
      description: Deletes a device using its unique ID.
      parameters:
        - name: id
          in: query
          description: Device Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
      responses:
        204:
          description: Device deleted successfully
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error
