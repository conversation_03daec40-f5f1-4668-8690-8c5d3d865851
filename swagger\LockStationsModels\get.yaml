
paths:
  # -------------> Get a Lock Station Model By id<-------------
  "/lockstations/models":
    get:
      tags:
        - Lock Stations MODELS
      summary: Get a lock station MODEL by Id.
      description: Get a lock station MODEL.
      parameters:
        - name: id
          in: query
          description: Model Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
      responses:
        200:
          description: Lock Station Model
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LockStationModel'
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error
