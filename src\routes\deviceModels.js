import express from 'express';
import devicesModelsController from '../controllers/deviceModels.js';
import authenticate from '../middlewares/authenticate.js';

const router = express.Router();

router.post('/', authenticate.authenticateToken, devicesModelsController.newDeviceModel);
router.delete('/', authenticate.authenticateToken, devicesModelsController.deleteDeviceModel);
router.put('/', authenticate.authenticateToken, devicesModelsController.updateDeviceModel);
router.get('/', authenticate.authenticateToken, devicesModelsController.getDeviceModel);
router.get('/all', authenticate.authenticateToken, devicesModelsController.getAllDevicesModels);

export default router;