<div style="position: relative; height: 237px">
  <canvas id="devices"></canvas>
  <div id="devices-spinner" style="
      position:absolute;
      top:0;
      left:0;
      width:100%;
      height:100%;
      display:flex;
      align-items:center;
      justify-content:center;
      ">
    <div class="spinner-border" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
</div>

<script>
  (function() {
    const chartEl = document.getElementById("devices").getContext("2d");
    const spinnerEl = document.getElementById("devices-spinner");

    const inUseRGB = getRGBFromCSSVariable('--active-rides');
    const parkedRGB = getRGBFromCSSVariable('--bike');
    const offlineRGB = getRGBFromCSSVariable('--bike-offline');

    let devicesChart = null;

    function renderChart(inUseDevices, parkedDevices, offlineDevices) {
      if (!devicesChart) {
        devicesChart = new Chart(chartEl, {
          type: "doughnut",
          data: {
            datasets: [{
              data: [inUseDevices, parkedDevices, offlineDevices],
              backgroundColor: [inUseRGB, parkedRGB, offlineRGB],
            }],
            labels: ["In use", "Parked", "Offline"],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            legend: {
              position: "right"
            },
            layout: {
              padding: {
                left: 20,
                right: 20,
                top: 20,
                bottom: 20
              }
            },
          },
        });
      } else {
        devicesChart.data.datasets[0].data = [inUseDevices, parkedDevices, offlineDevices];
        devicesChart.update();
      }
      spinnerEl.style.display = "none"; // hide spinner when chart is ready
    }

    document.addEventListener("DOMContentLoaded", function() {
      window.App.subscribe(["inUseDevices", "parkedDevices", "offlineDevices"], ({
        inUseDevices,
        parkedDevices,
        offlineDevices
      }) => {
        if (inUseDevices == null || parkedDevices == null || offlineDevices == null) {
          spinnerEl.style.display = "flex"; // show spinner if any value is null
          return;
        }
        renderChart(inUseDevices, parkedDevices, offlineDevices);
      });
    });
  })();
</script>