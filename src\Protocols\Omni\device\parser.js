class IoTMessageParser {
    constructor() {
        this.START_DELIMITER = '*';
        this.END_DELIMITER = '#';
        this.FIELD_SEPARATOR = ',';
        this.COMMAND_HEADER_SERVER_TO_IOT = '*SCOS';
        this.COMMAND_HEADER_IOT_TO_SERVER = '*SCOR';
    }

    parseUTCTimestamp(dateStr, timeStr) {
        const day = parseInt(dateStr.slice(0, 2), 10);
        const month = parseInt(dateStr.slice(2, 4), 10) - 1;
        const year = 2000 + parseInt(dateStr.slice(4, 6), 10);

        const hours = parseInt(timeStr.slice(0, 2), 10);
        const minutes = parseInt(timeStr.slice(2, 4), 10);
        const seconds = parseInt(timeStr.slice(4, 6), 10);

        const date = new Date(Date.UTC(year, month, day, hours, minutes, seconds));
        return date.toISOString();
    }
    parseLatitude(raw, hemisphere) {
        const degrees = parseInt(raw.slice(0, 2), 10);
        const minutes = parseFloat(raw.slice(2));
        let decimal = degrees + (minutes / 60);
        if (hemisphere === 'S') {
            decimal *= -1;
        }
        return decimal;
    }
    parseLongitude(raw, hemisphere) {
        const degrees = parseInt(raw.slice(0, 3), 10);
        const minutes = parseFloat(raw.slice(3));
        let decimal = degrees + (minutes / 60);
        if (hemisphere === 'W') {
            decimal *= -1;
        }
        return decimal;
    }
    parse(message) {
        const startIndex = message.indexOf(this.START_DELIMITER);
        if (startIndex === -1) {
            console.error("Parsing error: Could not find start delimiter " + this.START_DELIMITER);
            return null;
        }
        const endIndex = message.indexOf(this.END_DELIMITER, startIndex + 1);
        if (endIndex === -1) {
            console.error("Parsing error: Could not find end delimiter " + this.END_DELIMITER);
            return null;
        }

        // Include start delimiter, exclude end delimiter
        const rawPayload = message.substring(startIndex, endIndex);
        
        const parts = rawPayload.split(this.FIELD_SEPARATOR);

        if (parts.length < 4) {
            console.error("Error de parsing: Formato de comando inválido. Faltan partes.");
            return null;
        }

        const commandHeader = parts[0];
        const IMEI = parts[2];
        const CommandType = parts[3];
        const payloadFields = parts.slice(4);

        let parsedMessage = {
            raw: message,
            header: commandHeader,
            commandType: CommandType,
            imei: IMEI,
            payload: {},
            rawPayloadFields: payloadFields
        };
        switch (parsedMessage.commandType) {
            case 'Q0': //Check in command
                parsedMessage.payload = this._Command_Q0(payloadFields);
                break;
            case 'H0': //Heartbeat command
                parsedMessage.payload = this._Command_H0(payloadFields);
                break;
            case 'R0': //Lock or unlock request command
                parsedMessage.payload = this._Command_R0(payloadFields);
                break;
            case 'L0': //Unlock Command
                parsedMessage.payload = this._Command_L0(payloadFields);
                break;
            case 'L1': //Lock Command
                parsedMessage.payload = this._Command_L1(payloadFields);
                break;
            case 'S5': //IOT device settings
                parsedMessage.payload = this._Command_S5(payloadFields);
                break;
            case 'S6': //Vehicle Data Acquisition
                parsedMessage.payload = this._Command_S6(payloadFields);
                break;
            case 'S7': //Vehicle setting command1
                parsedMessage.payload = this._Command_S7(payloadFields);
                break;
            case 'S4': //Vehicle setting command 2
                parsedMessage.payload = this._Command_S4(payloadFields);
                break;
            case 'W0': //Alarm commands
                parsedMessage.payload = this._Command_W0(payloadFields);
                break;
            case 'V0': //AudibleAlertPlaybackCommand
                parsedMessage.payload = this._Command_V0(payloadFields);
                break;
            case 'D0': //Single Location Request Command
                parsedMessage.payload = this._Command_D0(payloadFields);
                break;
            case 'D1': //Real-time TrackingCommand，UsingD0 commanduploadlocation
                parsedMessage.payload = this._Command_D1(payloadFields);
                break;
            case 'G0': //Get firmware version
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
            case 'E0': //Upload controller error code
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
            case 'U0': //Check/lnitiate Upgrade <TCP way> 
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
            case 'U1': //Get upgrade dat）<TCP way> 
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
            case 'U2': //Upgrade success notification <TCP way>
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
            case 'K0': //Set/get BLE 8-byte communication KEY
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
            case 'S1': //Event notification command
                parsedMessage.payload = this._Command_S1(payloadFields);
                break;
            case 'L5': //External device control
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
            case 'Z0': //Get controller customized data
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
            case 'U5': //HTTPStartupUpgrade
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
            case 'U6': //HTTP Upgrade Status
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
            case 'I0': //Get SIM card ICCID
                parsedMessage.payload = this._Command_I0(payloadFields);
                break;
            case 'M0': //Get bluetooth MAC address
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
            case 'V1': //Device sounds settings
                parsedMessage.payload = this._Command_V1(payloadFields);
                break;
            case 'C0': //RFID card unlock request
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
            case 'B0': //Beacon Validation
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
            case 'SC': //Get Station Information
                parsedMessage.payload = this._Command_SC(payloadFields);
                break;
            default:
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
        }

        return parsedMessage;
    }

    //////////////////////////    
    //IOT to SERVER COMMANDS//
    //////////////////////////


    /**
     * Check-in command - Q0
     *   IoT automatically sends this command as the first transmission upon
     *   every connection to the server (including reconnection after
     *   disconnection). No response is needed
     * @param {string[]} fields Payload.
     * @returns {object} 
     */
    _Command_Q0(fields) {
        try {
            if (fields.length < 3) {
                console.warn("Advertencia: El comando Q0 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            return {
                Voltage_internal: fields[0] / 100,
                battery_level: fields[1],
                signal_strength: Math.round((fields[2] - 2) * 100 / 30),
                event: 'Connection'
            };
        } catch (error) {
            return {};
        }
    }

    /**
     * Heartbeat command - H0
     *   The lock transmits this command at regular intervals (default: every 4
     *   minutes) to maintain the connection with the server.
     * @param {string[]} fields Payload.
     * @returns {object} 
     */
    _Command_H0(fields) {
        try {
            if (fields.length < 5) {
                console.warn("Advertencia: El comando H0 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            return {
                status: fields[0] == 0 ? 'Unlocked' : 'Locked',
                Voltage_internal: fields[1] / 100,
                battery_level: fields[3], //Invertido respecto al reporte Q0 (Son un desastre estos chinos)
                signal_strength: Math.round((fields[2] - 2) * 100 / 30),
                charging: fields[4] == 0 ? false : true, //Logica inversa
                event: 'HeartBeat'
            };
        } catch (error) {
            return {};
        }
    }

    /**
     * Unlock/Lock - R0
     *   Unlock/Lock operation request command
     * @param {string[]} fields Payload.
     * @returns {object} 
     */
    _Command_R0(fields) {
        try {
            if (fields.length < 4) {
                console.warn("Advertencia: El comando R0 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            var request;
            var event;
            switch (fields[0]) {
                case '0':
                    request = "Unlock";
                    event = 'UnlockRequest'
                    break;
                case '1':
                    request = "Lock";
                    event = 'LockRequest'
                    break;
                case '2':
                    request = "RFID Unlock";
                    break;
                case '3':
                    request = "RFID lock";
                    break;
                default:
                    request = "Error";
                    break;
            }
            return {
                Request: request,
                Key: fields[1],
                UserId: fields[2], //Invertido respecto al reporte Q0 (Son un desastre estos chinos)
                ts: fields[3],
                event: event
            };
        } catch (error) {
            return {};
        }
    }

    /**
     * Unlock - L0
     *   Unlock Command ,Requires execute R0 command before issuing any unlock operation
     * @param {string[]} fields Payload.
     * @returns {object} 
     */
    _Command_L0(fields) {
        try {
            if (fields.length < 3) {
                console.warn("Advertencia: El comando L0 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            var Status;
            var status;
            switch (fields[0]) {
                case '0':
                    Status = "Success";
                    status = "Unlocked";
                    break;
                case '1':
                    Status = "Failure";
                    break;
                case '2':
                    Status = "Error";
                    break;
                default:
                    Status = "Error";
                    break;
            }
            if (fields[1] == "undefined") return {};
            if (fields[2] == "undefined") return {};
            return {
                Status: Status,
                status: status,
                userId: fields[1], //Invertido respecto al reporte Q0 (Son un desastre estos chinos)
                ts: fields[2],
                event: 'Unlock'
            };
        } catch (error) {
            return {};
        }
    }

    /**
    * Lock - L1
    *   Lock Command ,Requires execute R0 command before issuing any lock operation
    * @param {string[]} fields Payload.
    * @returns {object} 
    */
    _Command_L1(fields) {
        try {
            if (fields.length < 4) {
                console.warn("Advertencia: El comando L1 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            var Status;
            var status;
            switch (fields[0]) {
                case '0':
                    Status = "Success";
                    status = "Locked";
                    break;
                case '1':
                    Status = "Failure";
                    break;
                case '2':
                    Status = "Key Error";
                    break;
                case '3':
                    Status = "Riding";
                    break;
                case '4':
                    Status = "Timeout";
                    break;
                case '5':
                    Status = "Rejected";
                    break;
                default:
                    Status = "Error";
                    break;
            }
            return {
                Status: Status,
                status: status,
                UserId: fields[1], //Invertido respecto al reporte Q0 (Son un desastre estos chinos)
                ts: fields[2],
                riding_Time: fields[3],
                event: 'Lock'
            };
        } catch (error) {
            return {};
        }
    }

    /**
    * Settings - S5
    *   IoT Device Configuration Command
    *   This command used to modify operational parameters or settings of IoT
    *   devices
    * @param {string[]} fields Payload.
    * @returns {object} 
    */
    _Command_S5(fields) {
        try {
            if (fields.length < 4) {
                console.warn("Advertencia: El comando S5 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            var Accelerometer;
            var Upload;
            switch (fields[0]) {
                case '1':
                    Accelerometer = "Low";
                    break;
                case '2':
                    Accelerometer = "Midium";
                    break;
                case '3':
                    Accelerometer = "High";
                    break;
                default:
                    Accelerometer = "Error";
                    break;
            }
            switch (fields[1]) {
                case '0':
                    Upload = "Invalid";
                    break;
                case '1':
                    Upload = "Off";
                    break;
                case '2':
                    Upload = "On";
                    break;
                default:
                    Upload = "Error";
                    break;
            }
            return {
                Accelerometer: Accelerometer,
                Upload: Upload, //Invertido respecto al reporte Q0 (Son un desastre estos chinos)
                Heartbeat: fields[2],
                UploadInterval: fields[3],
                event: 'Configuration'
            };
        } catch (error) {
            return {};
        }
    }

    /**
    * Vehicle Data Acquisition - S6
    *   Vehicle Data Acquisition
    * @param {string[]} fields Payload.
    * @returns {object} 
    */
    _Command_S6(fields) {
        try {
            if (fields.length < 8) {
                console.warn("Advertencia: El comando S6 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            var Speed;
            switch (fields[1]) {
                case '1':
                    Speed = "Low";
                    break;
                case '2':
                    Speed = "Midium";
                    break;
                case '3':
                    Speed = "High";
                    break;
                default:
                    Speed = "Error";
                    break;
            }
            return {
                Voltage_internal: fields[0],
                SpeedMode: Speed,
                Speed: fields[2],
                Charging: fields[3] == '0' ? false : true,
                battery_level: fields[4] / 10,
                Battery2: fields[5] / 10,
                Unlocked: fields[6] == '0' ? true : false,
                signal_strength: Math.round((field[7] - 2) * 10 / 3)
            };
        } catch (error) {
            return {};
        }
    }

    /**
    * Vehicle setting command1 - S7
    *   Vehicle setting command1
    *   Note: The following settings will not be saved after power off, It will be restored to default values after restart or unlock.
    * @param {string[]} fields Payload.
    * @returns {object} 
    */

    _Command_S7(fields) {
        try {
            if (fields.length < 4) {
                console.warn("Advertencia: El comando S7 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            var Headlight;
            var SpeedMode;
            var ThrottleSettings;
            var Taillight_Flashing_Mode;
            switch (fields[0]) {
                case '1':
                    Headlight = false;
                    break;
                case '2':
                    Headlight = true;
                    break;
                default:
                    Headlight = "Error";
                    break;
            }
            switch (fields[1]) {
                case '1':
                    SpeedMode = "Low";
                    break;
                case '2':
                    SpeedMode = "Medium";
                    break;
                case '3':
                    SpeedMode = "High";
                    break;
                default:
                    SpeedMode = "Error";
                    break;
            }
            switch (fields[2]) {
                case '1':
                    ThrottleSettings = false;
                    break;
                case '2':
                    ThrottleSettings = true;
                    break;
                default:
                    ThrottleSettings = "Error";
                    break;
            }
            switch (fields[3]) {
                case '1':
                    Taillight_Flashing_Mode = false;
                    break;
                case '2':
                    Taillight_Flashing_Mode = true;
                    break;
                default:
                    Taillight_Flashing_Mode = "Error";
                    break;
            }
            return {
                Headlight: Headlight,
                SpeedMode: SpeedMode, //Invertido respecto al reporte Q0 (Son un desastre estos chinos)
                ThrottleSettings: ThrottleSettings,
                Taillight_Flashing_Mode: Taillight_Flashing_Mode
            };
        } catch (error) {
            return {};
        }
    }

    /**
    * Vehicle setting command2 - S4
    *   Vehicle setting command2
    *   Note: IoT device retains the below values when power off and IoT will report the current values to server
    * @param {string[]} fields Payload.
    * @returns {object} 
    */

    _Command_S4(fields) {
        try {
            if (fields.length < 8) {
                console.warn("Advertencia: El comando S4 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            var SpeedUnits;//(mph)
            var CruiceControl;
            var StartMode;
            var SpeedModeButton;
            var HeadLightButton;
            switch (fields[0]) {
                case '0':
                    SpeedUnits = false;
                    break;
                case '1':
                    SpeedUnits = false;
                    break;
                case '2':
                    SpeedUnits = true;
                    break;
                default:
                    SpeedUnits = "Error";
                    break;
            }
            switch (fields[1]) {
                case '0':
                    CruiceControl = false;
                    break;
                case '1':
                    CruiceControl = false;
                    break;
                case '2':
                    CruiceControl = true;
                    break;
                default:
                    CruiceControl = "Error";
                    break;
            }
            switch (fields[2]) {
                case '0':
                    StartMode = false;
                    break;
                case '1':
                    StartMode = false;
                    break;
                case '2':
                    StartMode = true;
                    break;
                default:
                    StartMode = "Error";
                    break;
            }
            switch (fields[3]) {
                case '0':
                    SpeedModeButton = true;
                    break;
                case '1':
                    SpeedModeButton = false;
                    break;
                case '2':
                    SpeedModeButton = true;
                    break;
                default:
                    SpeedModeButton = "Error";
                    break;
            }
            switch (fields[4]) {
                case '0':
                    HeadLightButton = true;
                    break;
                case '1':
                    HeadLightButton = false;
                    break;
                case '2':
                    HeadLightButton = true;
                    break;
                default:
                    HeadLightButton = "Error";
                    break;
            }
            return {
                SpeedUnits: SpeedUnits,
                CruiceControl: CruiceControl, //Invertido respecto al reporte Q0 (Son un desastre estos chinos)
                StartMode: StartMode,
                SpeedModeButton: SpeedModeButton,
                HeadLightButton: HeadLightButton,
                MaxSpeed_Low: fields[5],
                MaxSpeed_Mid: fields[6],
                MaxSpeed_Hig: fields[7],
            };
        } catch (error) {
            return {};
        }
    }

    /**
     * Alarm commands - W0
     *   Alarm commands
     * @param {string[]} fields Payload.
     * @returns {object} 
     */

    _Command_W0(fields) {
        try {
            if (fields.length < 1) {
                console.warn("Advertencia: El comando W0 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            var Alarm;
            switch (fields[0]) {
                case '1':
                    Alarm = "Unauthorized movement";
                    break;
                case '2':
                    Alarm = "Tip-over";
                    break;
                case '3':
                    Alarm = "Unauthorized disassembly";
                    break;
                case '4':
                    Alarm = "Low Battery";
                    break;
                case '6':
                    Alarm = "Tip-over Cleared";
                    break;
                case '7':
                    Alarm = "Unauthorized disassembly Cleared";
                    break;
                default:
                    Alarm = "Error";
                    break;
            }
            return {
                Alarm: Alarm,
            };
        } catch (error) {
            return {};
        }
    }

    /**
     * Audible Playbackcommands - V0
     *   Audible playback Alerts
     * @param {string[]} fields Payload.
     * @returns {object} 
     */

    _Command_V0(fields) {
        try {
            if (fields.length < 1) {
                console.warn("Advertencia: El comando V0 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            var Audio;
            switch (fields[0]) {
                case '1':
                    Audio = "Out-of-Range";
                    break;
                case '2':
                    Audio = "Find Vehicle";
                    break;
                case '3':
                    Audio = "Low Battery";
                    break;
                case '80':
                    Audio = "turn off Alert sound";
                    break;
                case '81':
                    Audio = "turn on Alert sound";
                    break;
                case '91':
                    Audio = "Customized";
                    break;
                default:
                    Audio = "Error";
                    break;
            }
            return {
                Audio: Audio,
            };
        } catch (error) {
            return {};
        }
    }


    /**
    * Location - D0
    *   Location request command.
    * @param {string[]} fields Payload.
    * @returns {object} 
    */

    _Command_D0(fields) {
        try {
            if (fields.length < 13) {
                console.warn("Advertencia: El comando D0 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            var trigger;
            var Valid;

            switch (fields[0]) {
                case '0':
                    trigger = "Command";
                    break;
                case '1':
                    trigger = "Continuous";
                    break;
                default:
                    trigger = "Continuous";
                    break;
            }
            switch (fields[2]) {
                case 'A':
                    Valid = true;
                    break;
                case 'V':
                    Valid = false;
                    break;
                default:
                    Valid = "Error";
                    break;
            }
            const latitude = this.parseLatitude(fields[3], fields[4]);
            const longitude = this.parseLongitude(fields[5], fields[6])
            const satellites = parseInt(fields[7], 10);
            const HDOP = parseFloat(fields[8]);
            const ts = this.parseUTCTimestamp(fields[9], fields[1]);
            const altitude = parseInt(fields[10]);
            var mode;

            switch (fields[12]) {
                case 'A':
                    mode = "Autonomous";
                    break;
                case 'D':
                    mode = "Differential";
                    break;
                case 'E':
                    mode = "Estimated";
                    break;
                case 'N':
                    mode = "Invalid";
                    break;
                default:
                    mode = "Error";
                    break;
            }
            return {
                trigger: trigger,
                Valid: Valid, //Invertido respecto al reporte Q0 (Son un desastre estos chinos)
                lat: latitude,
                lon: longitude,
                satellites: satellites,
                HDOP: HDOP,
                ts: ts,
                Altitude: altitude,
                mode: mode,
                event: 'Position',
            };
        } catch (error) {
            return {};
        }
    }

    /**
     * Positioning settings - D1
     *   Real time tracking Command,Using D0 command upload location
     * @param {string[]} fields Payload.
     * @returns {object} 
     */

    _Command_D1(fields) {
        try {
            if (fields.length < 1) {
                console.warn("Advertencia: El comando D1 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            return {
                frecuency: parseInt(fields[0], 10)
            };
        } catch (error) {
            return {};
        }
    }

    /**
     * Event command - S1
     *   Event command
     * @param {string[]} fields Payload.
     * @returns {object} 
     */

    _Command_S1(fields) {
        try {
            if (fields.length < 1) {
                console.warn("Advertencia: El comando S1 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            var event;
            switch (fields[0]) {
                case '1':
                    event = "IOT Off";
                    break;
                case '2':
                    event = "IOT Restart";
                    break;
                case '8':
                    event = "Vehicle within permitted parking zone";
                    break;
                case '9':
                    event = "Vehicle outside permitted parking zone";
                    break;
                case '10':
                    event = "Vehicle Reserved";
                    break;
                case '11':
                    event = "Reservation Canceled";
                    break;
                case '12':
                    event = "Vehicle Faulty";
                    break;
                case '13':
                    event = "Faulty Canceled";
                    break;
                case '16':
                    event = "Vehicle Lost";
                    break;
                case '17':
                    event = "Lost Canceled";
                    break;
                default:
                    event = "error";
                    break;
            }
            return {
                event: event
            };
        } catch (error) {
            return {};
        }
    }

    /**
     * Get SIm ICCID - I0
     *   Get the SIM card ICCID number
     * @param {string[]} fields Payload.
     * @returns {object} 
     */

    _Command_I0(fields) {
        try {
            if (fields.length < 1) {
                console.warn("Advertencia: El comando I0 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            return {
                ICCID: fields[0]
            };
        } catch (error) {
            return {};
        }
    }

    /**
     * IoT Device Sound Settings - V1
     *   IoT Device Sound Settings
     * @param {string[]} fields Payload.
     * @returns {object} 
     */

    _Command_V1(fields) {
        try {
            if (fields.length < 4) {
                console.warn("Advertencia: El comando V1 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            var AlarmSound = true;
            var UnlockSound = true;
            var LockSound = true;
            if (fields[0] == '1') AlarmSound = false;
            if (fields[1] == '1') UnlockSound = false;
            if (fields[2] == '1') LockSound = false;

            return {
                AlarmSound: AlarmSound,
                UnlockSound: UnlockSound,
                LockSound: LockSound
            };
        } catch (error) {
            return {};
        }
    }

    /**
     * TODO
     * Get station information - SC
     *   Get station information
     * @param {string[]} fields Payload.
     * @returns {object} 
     */

    _Command_SC(fields) {
        try {
            if (fields.length < 15) {
                console.warn("Advertencia: El comando SC tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            var IMEI = null;
            IMEI = fields[0];
            var ChargingStatus;
            switch (fields[3]) {
                case '0':
                    ChargingStatus = "idle"
                    break;
                case '1':
                    ChargingStatus = "trickle"
                    break;
                case '2':
                    ChargingStatus = "constant current"
                    break;
                case '3':
                    ChargingStatus = "constant voltage"
                    break;
                case '4':
                    ChargingStatus = "complete"
                    break;


                default:
                    break;
            }
            var Status;
            switch (fields[1]) {
                case '0':
                    Status = "Locked"
                    break;
                case '1':
                    Status = "Unlocked"
                    break;
                default:
                    Status = "Unknown"
                    break;
            }

            return {
                event: 'StationRequest',
                Status: Status,
                ChargingStatus: ChargingStatus,
                IMEI: IMEI
            };
        } catch (error) {
            return {};
        }
    }

    _parseGenericCommand(fields) {
        return { fields: fields };
    }
}

export default IoTMessageParser;