//Tenants configure payment method, and customers can get it
import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const Configuration = sequelize.define('Configuration', {
  currency: {
    type: DataTypes.ENUM,
    values: ['FREE', 'FIXED', 'TIME', 'KM', 'WALLETFIX', 'WALLET<PERSON><PERSON>', 'WALLETKM'],
    allowNull: false,
    defaultValue: 'FREE'
  },
  price: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  // The tenantId is already defined in the associations
  // It is already added here, to set the unique constraint
  tenantId: {
    type: DataTypes.INTEGER, // Same type as User id
    allowNull: false,
    unique: true,
  }
});

export default Configuration;