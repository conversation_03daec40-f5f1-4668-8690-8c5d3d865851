
paths:
  # -------------> get a Lock Station <-------------
  "/lockstations":
    get:
      tags:
        - Lock Stations
      summary: Get a Lock Station.
      description: Get a Lock Station.
      parameters:
        - name: id
          in: query
          description: Lock Station Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
      responses:
        200:
          description: Lock Stations
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LockStation'
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error