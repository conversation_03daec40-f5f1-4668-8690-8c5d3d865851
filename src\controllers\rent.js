import validate from "../middlewares/validate.js";
import Device from "../models/device.js";
import DevicesModel from "../models/deviceModels.js";
import LockStation from "../models/lockStations.js";
import LockStationsModel from "../models/lockStationsModels.js";
import Rents from "../models/rents.js";
import Station from "../models/station.js";
import User from "../models/users.js";
import iotFunctions from '../Protocols/functions.js';
import authenticate from '../middlewares/authenticate.js';
import { Op } from 'sequelize';


const rentDevice = async (req, res) => {
    try {
        const user = req.user;
        const { serial } = req.body;
        
        if (user.type !== 'Customer') return res.status(400).send({ error: "Only Customer users can rent devices" });
        if (!serial) return res.status(400).send({ error: "No serial provided" });

        if (0 >= user.credit) return res.status(400).send({ error: "Not enough credit" });

        const device = await Device.findOne({
            where: { serial: serial },
            include: [
                {
                    model: DevicesModel,
                    as: 'relatedModel'
                },
                {
                    model: LockStation,
                    as: 'relatedLockStation',
                    include: [
                        {
                            model: LockStationsModel,
                            as: 'relatedModel',
                        }
                    ]
                }
            ]
        });

        if (!device) return res.status(404).send("Device not found");
        if (!device.connected) return res.status(400).send({ error: "Device not connected" });
        if (device.customerId) return res.status(400).send({ error: "Device already assigned to another user" });
        if (!device.relatedLockStation) return res.status(400).send({ error: "Could not find the lock station associated to the device" });
        
        let lockStation = device.relatedLockStation;
        let lockStationIndex = device.stationIndex - lockStation.stationStartIndex + 1; // Starts at 1

        if (lockStationIndex > lockStation.relatedModel.spots) {
            return res.status(400).send({ error: "The device is associated to an invalid lock station index" });
        }

        //Limpieza del estado si quedó mal
        if (["Requesting", "Unlocking", "Locking"].includes(device.status)) {
            let currentTime = Math.floor(Date.now() / 1000);
            const expired = !device.locking_parameters ||
                (currentTime - device.locking_parameters.timestamp > (device.locking_parameters.validity));

            if (expired) {
                if (device.locking_parameters.lock) device.status = 'Rented';
                else device.status = 'Ready';
                device.locking_parameters = {};
                await device.save();
            }
        }

        if (device.status !== 'Ready') {
            return res.status(400).send({ error: `Device status must be Ready. Current: '${device.status}'` });
        }

        //Cambio de estado y setup de locking
        device.status = 'Requesting';
        device.locking_parameters = {
            timestamp: Math.floor(Date.now() / 1000),
            lock: false,
            validity: 20,
            userid: user.id,
            paymentMethod: 'Credit',
        };
        await device.save();

        // It is necessary to be detected as a change in sequelize
        let spotsStatus = { ...lockStation.spotsStatus };
        spotsStatus[lockStationIndex] = 'Unlocking';
        lockStation.spotsStatus = spotsStatus;
        await lockStation.save();

        //Enviar comando al dispositivo
        const unlockResponse = await iotFunctions.sendUnlock(device, user.id);

        if (unlockResponse.code !== 200) {
            device.status = 'Ready';
            device.locking_parameters = {};
            await device.save();
            return res.status(400).send({ error: unlockResponse.error });
        }

        //Esperar hasta que el dispositivo pase a Rented o falle 
        const result = await Promise.race([
            _waitForStatusChange(serial, 'Rented', device.locking_parameters.validity * 1000),
            new Promise(resolve => setTimeout(() => resolve('Timeout'), device.locking_parameters.validity * 1000))
        ]);

        const updatedDevice = await Device.findOne({ where: { serial: serial } });

        if (result === 'Timeout') {
            updatedDevice.status = 'Ready';
            updatedDevice.locking_parameters = {};
            updatedDevice.customerId = null;
            await updatedDevice.save();
            return res.status(408).send({ error: "Timeout esperando al dispositivo" });
        }

        if (updatedDevice.customerId !== user.id) {
            return res.status(400).send({ error: "Device already rented by another user" });
        }

        return res.status(200).send({ message: "Device rented" });

    } catch (error) {
        console.error(error);
        res.status(500).json({ error: error.message || error });
    }
}

const getRentStartTime = async (req, res) => {
    try {
        const user = req.user;
        const { deviceId } = req.query;

        if (!deviceId) return res.status(400).send({ error: "device id not provided" });
        if (!validate.isUUID(deviceId)) return res.status(400).send({ error: "device id provided is not an uuid" })

        const rent = await Rents.findOne({
            where: {deviceId: deviceId},
            order: [['createdAt', 'DESC']],
            limit: 1,
            separate: true, // It can avoid problems in limiting and ordering
        });
        if (!rent) {
            return res.status(404).send({ error: "Rent not found" });
        }
        let startTime = new Date(rent.startTime).getTime();

        return res.status(200).json({ 'startTime': startTime });
    } catch (error) {
        res.status(500).json({ error: error });
    }
}

const debugCreateRent = async (req, res) => {
    try {
        const user = req.user;
        const { 
            startTime, totalTime, 
            customerId, deviceId, startStationId, endStationId,
            paymentMethod, price 
        } = req.body;

        if (!authenticate.hasAuthority("Tenant", user)) return res.status(400).send({ error: "Only tenant users are allowed to create debug rents" });

        if ((await User.findByPk(customerId)) == null) return res.status(400).send({ error: "User not found" });
        if ((await Device.findByPk(deviceId)) == null) return res.status(400).send({ error: "Device not found" });
        if ((await Station.findByPk(startStationId)) == null) return res.status(400).send({ error: "Start station not found" });
        if ((await Station.findByPk(endStationId)) == null) return res.status(400).send({ error: "End station not found" });

        const rent = await Rents.create({
            startTime: startTime,
            customerId: customerId,
            deviceId: deviceId,
            startStation: startStationId,
            paymentMethod: paymentMethod,

            endTime: startTime + totalTime*1000,
            totalTime: totalTime,
            endStation: endStationId,
            price: price,
        });
        res.status(200).json(rent);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: error.message || error });
    }
}

function _waitForStatusChange(serial, expectedStatus, timeoutMs) {
    return new Promise((resolve) => {
        const interval = setInterval(async () => {
            const device = await Device.findOne({ where: { serial } });
            if (device.status === expectedStatus) {
                clearInterval(interval);
                resolve('OK');
            }
        }, 1000);

        setTimeout(() => {
            clearInterval(interval);
            resolve('Timeout');
        }, timeoutMs);
    });
}

/**
 * Get the number of rides grouped by hour
 * @returns {Promise<void>} JSON response with rides per hour
 */
const getRidesPerHour = async (req, res) => {
    try {
        const rides = await Rents.findAll({
            attributes: [
                [Rents.sequelize.fn('DATE_TRUNC', 'hour', Rents.sequelize.col('startTime')), 'hour'],
                [Rents.sequelize.fn('COUNT', '*'), 'count']
            ],
            group: [Rents.sequelize.fn('DATE_TRUNC', 'hour', Rents.sequelize.col('startTime'))],
            order: [[Rents.sequelize.fn('DATE_TRUNC', 'hour', Rents.sequelize.col('startTime')), 'ASC']]
        });
        res.json(rides);
    } catch (error) {
        res.status(500).json({ error: 'Failed to get rides per hour' });
    }
};

/**
 * Get the number of rides grouped by day of the week
 * @returns {Promise<void>} JSON response with rides per day of week
 */
const getRidesPerWeek = async (req, res) => {
    try {
        const rides = await Rents.findAll({
            attributes: [
                [Rents.sequelize.fn('EXTRACT', Rents.sequelize.literal('DOW FROM "startTime"')), 'dayOfWeek'],
                [Rents.sequelize.fn('COUNT', '*'), 'count']
            ],
            group: [Rents.sequelize.fn('EXTRACT', Rents.sequelize.literal('DOW FROM "startTime"'))],
            order: [[Rents.sequelize.fn('EXTRACT', Rents.sequelize.literal('DOW FROM "startTime"')), 'ASC']]
        });

        // map numbers of the week to names
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const formattedRides = rides.map(ride => ({
            dayOfWeek: dayNames[ride.dataValues.dayOfWeek],
            dayNumber: ride.dataValues.dayOfWeek,
            count: parseInt(ride.dataValues.count)
        }));

        res.json(formattedRides);
    } catch (error) {
        console.error('Error getting rides per week:', error);
        res.status(500).json({ error: 'Failed to get rides per week' });
    }
};

/**
 * Get the number of rides grouped by month
 * @returns {Promise<void>} JSON response with rides per month with formatted month names
 */
const getRidesPerMonth = async (req, res) => {
    try {
        const rides = await Rents.findAll({
            attributes: [
                [Rents.sequelize.fn('DATE_TRUNC', 'month', Rents.sequelize.col('startTime')), 'month'],
                [Rents.sequelize.fn('COUNT', '*'), 'count']
            ],
            group: [Rents.sequelize.fn('DATE_TRUNC', 'month', Rents.sequelize.col('startTime'))],
            order: [[Rents.sequelize.fn('DATE_TRUNC', 'month', Rents.sequelize.col('startTime')), 'ASC']]
        });

        const formattedRides = rides.map(ride => ({
            month: new Date(ride.dataValues.month).toLocaleDateString('en-US', { year: 'numeric', month: 'long' }),
            count: parseInt(ride.dataValues.count)
        }));

        res.json(formattedRides);
    } catch (error) {
        console.error('Error getting rides per month:', error);
        res.status(500).json({ error: 'Failed to get rides per month' });
    }
};

const getRidesPerKm = async (req, res) => {
    //TODO: implement function
    res.status(501).json({ error: 'Function not yet implemented' });
};

export default { rentDevice, getRentStartTime, debugCreateRent, getRidesPerHour, getRidesPerWeek, getRidesPerMonth, getRidesPerKm };
