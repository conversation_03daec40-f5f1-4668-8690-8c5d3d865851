import sequelize from '../config/database.js';
import Server from '../models/server.js';

import dotenv from 'dotenv';
import { DataTypes } from 'sequelize';
const queryInterface = sequelize.getQueryInterface();

dotenv.config();
const versionUpdater = async () => {
    try {
        var server = await Server.findByPk(1);
        if (!server) {
            server = await Server.create({ version: process.env.SERVER_VERSION })
        }
        if (server.version != process.env.SERVER_VERSION) {
            //Postrgres tables update process
            if (server.version == 1) {
                await queryInterface.addColumn('Users', 'rjwt', { type: DataTypes.STRING });
                server.set({
                    version: 2
                });
                await server.save();
            }
            if (server.version == 2) {
                await queryInterface.addColumn('Users', 'tenantId', { type: DataTypes.UUID, allowNull: true });
                server.set({
                    version: 3
                });
                await server.save();
            }
            if (server.version == 3) {
                await queryInterface.addColumn('Users', 'deleted', { type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false });
                server.set({
                    version: 4
                });
                await server.save();
            }
            if (server.version == 4) {
                // If there were lock stations before the station start index addition, there are set to 1
                await queryInterface.addColumn('LockStations', 'stationStartIndex', { type: DataTypes.INTEGER, allowNull: false, unique: false, defaultValue: 1 });
                await queryInterface.addColumn('Devices', 'stationIndex', { type: DataTypes.INTEGER, allowNull: true });
                server.set({
                    version: 5
                });
                await server.save();
            }
            if (server.version == 5) {
                await queryInterface.addColumn('LockStations', 'connected', { type: DataTypes.BOOLEAN, allowNull: true });
                await queryInterface.addColumn('Telemetries', 'lockStationId', {
                    type: DataTypes.UUID,
                    references: {
                        model: 'LockStations',
                        key: 'id',
                    },
                });

                server.set({
                    version: 6
                });
                await server.save();
            }
            if (server.version == 6) {
                // If there were devices before the qr code addition, there are set equal to the serial
                await queryInterface.addColumn('Devices', 'qrCode', {
                    type: DataTypes.STRING,
                    allowNull: true, // temporarily allow nulls
                });
                await queryInterface.sequelize.query(`
                    UPDATE "Devices"
                    SET "qrCode" = "serial"
                `);
                await queryInterface.changeColumn('Devices', 'qrCode', {
                    type: DataTypes.STRING,
                    allowNull: false,
                    unique: true,
                });
                server.set({
                    version: 7
                });
                await server.save();
            }
            if (server.version == 7) {
                await queryInterface.addColumn('LockStations', 'spotsStatus', {
                    type: DataTypes.JSON,
                    allowNull: false,
                    defaultValue: {},
                    get() {
                        return this.getDataValue("spotsStatus");
                    },
                    set(value) {
                        return this.setDataValue("spotsStatus", value);
                    }
                });
                server.set({
                    version: 8
                });
                await server.save();
            }
            if (server.version == 8) {
                await queryInterface.addColumn('Rents', 'customerId', {
                    type: DataTypes.INTEGER,
                    references: {
                        model: 'Users',
                        key: 'id',
                    },
                });
                await queryInterface.addColumn('Rents', 'deviceId', {
                    type: DataTypes.UUID,
                    references: {
                        model: 'Devices',
                        key: 'id',
                    },
                });

                await queryInterface.removeColumn('Rents', 'startTime');
                await queryInterface.addColumn('Rents', 'startTime', {
                    type: DataTypes.DATE,
                    allowNull: false,
                });
                await queryInterface.removeColumn('Rents', 'endTime');
                await queryInterface.addColumn('Rents', 'endTime', {
                    type: DataTypes.DATE,
                    allowNull: true,
                });

                await queryInterface.addColumn('Users', 'credit', {
                    type: DataTypes.DOUBLE,
                    allowNull: false,
                    defaultValue: 0.0,
                });
                
                await queryInterface.changeColumn('Configurations', 'tenantId', {
                    type: DataTypes.UUID,
                    unique: true,
                    references: {
                        model: 'Users',
                        key: 'id',
                    },
                });
                await queryInterface.addConstraint('Configurations', {
                    fields: ['tenantId'],
                    type: 'unique',
                    name: 'configurations_tenantId_unique'
                });
                await queryInterface.changeColumn('Rents', 'price', {
                    type: DataTypes.DOUBLE,
                    allowNull: true,
                });

                server.set({
                    version: 9
                });
                await server.save();
            }
            if (server.version == 9) {
                // All the stations previously saved are set with the OMNI protocol
                await queryInterface.addColumn('Stations', 'protocol', { type: DataTypes.STRING, allowNull: false, defaultValue: "OMNI" });

                server.set({
                    version: 10
                });
                await server.save();
            }
        }
        return;
    } catch (error) {
        console.log(error);
    }

};

export default versionUpdater;
