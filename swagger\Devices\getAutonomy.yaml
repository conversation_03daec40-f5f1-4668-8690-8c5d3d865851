  # -------------> Get Device autonomy by Id <-------------
paths:
  "/devices/autonomy":
    get:
      tags:
        - Devices
      summary: Get a device autonomy by ID
      parameters:
        - name: id
          in: query
          description: Device Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
      responses:
        200:
          description: Device autonomy found
          content:
            application/json:
              schema:
                type: object
                properties:
                  batteryLevel:
                    type: integer
                    description: Device battery level
                    example: 50
                  kilometers:
                    type: number
                    description: Device available kilometers
                    example: 10.5
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error