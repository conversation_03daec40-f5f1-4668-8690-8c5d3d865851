
paths:
  # -------------> Get Device Model by ID <-------------
  "/devices/models":
    get:
      tags:
        - Devices MODELS
      summary: Get a device model by ID
      description: Retrieves a single device model using its unique ID.
      parameters:
        - name: id
          in: query
          description: Model Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
      responses:
        200:
          description: Device model retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceModel'
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error
