import api from './serverApi.js';

const login = (req, res) => {
  res.render('loginManagement');
};

const init = (req, res) => {
  res.render('index');
};

const map = async (req, res) => {
  res.render('map');
};

const mapRefresh = async (req, res) => {
  try {
    const homeData = await _getHomeData(req);
    res.json(homeData);
  } catch (error) {
    // _getHomeData already logs the error
    res.status(500).json({error: 'Failed to refresh data'});
  }
};

const devices = async (req, res) => {
  try {
    const devices = await api.getDevices(req);
    const models = await api.getDeviceModels(req);

    // Create a lookup map for modelId -> modelName
    const modelMap = {};
    models.forEach(model => {
      modelMap[model.id] = model.name;
    });

    // Replace modelId with modelName in devices
    const enrichedDevices = devices.map(
        device => ({
          ...device,
          modelName: modelMap[device.modelId] || `Unknown (${device.modelId})`
        }));

    res.render('devices', {devices: enrichedDevices, models});
  } catch (error) {
    console.error(error);
    res.render('devices', {devices: []});
  }
};

const addDevice = async (req, res) => {
  const {serial, imei, type, model, phone, description} = req.body;

  try {
    await api.createDevice(
        req, {serial, qrCode: imei, modelId: model, phone, type, description});
    res.redirect('/devices');
  } catch (error) {
    console.error(error);
    res.redirect('/devices');  // We should render an error page here or show an
                               // error message
  }
};

const deleteDevice = async (req, res) => {
  const {id} = req.params;

  try {
    await api.deleteDevice(req, id);
    res.status(200).json({message: 'Device deleted successfully'});
  } catch (error) {
    console.error(error);
    res.status(500).json({error: 'Internal server error'});
  }
};

async function _getHomeData(req) {
  try {
    const stations = await api.getStations(req);
    const lockStations = await api.getLockStations(req);
    const devices = await api.getDevices(req);
    const devicesPositions = await api.getDevicesPosition(req);
    const totalUsers = await api.getTotalUsers(req);
    const ridingDevices = devices.filter(device => device.status === 'Rented');
    var onlineStations = 0;
    var offlineStations = 0;
    var partialServiceStations = 0;
    stations.forEach(station => {
      let locks = lockStations.filter(
          lockStation => station.id === lockStation.stationId);
      let onlineLocks =
          locks.filter(lockStation => lockStation.connected === true);
      let offlineLocks =
          locks.filter(lockStation => lockStation.connected !== true);
      if (onlineLocks.length === 0) {
        offlineStations++;
      } else if (offlineLocks.length === 0) {
        onlineStations++;
      } else {
        partialServiceStations++;
      }
    });
    var inUseDevices = 0;
    var parkedDevices = 0;
    var offlineDevices = 0;
    devices.forEach(device => {
      let devicePosition = devicesPositions.find(
          devicePosition => device.id === devicePosition.deviceId);
      if (devicePosition) {
        device.lat = devicePosition.lat;
        device.lon = devicePosition.lon;
      } else {
        device.lat = null;
        device.lon = null;
      }
      if (device.connected !== true) {
        offlineDevices++;
      } else if (device.status === 'Rented') {
        inUseDevices++;
      } else {
        parkedDevices++;
      }
    });
    return {
      stations,
      devices,
      ridingDevices,
      totalUsers,
      onlineStations,
      offlineStations,
      partialServiceStations,
      inUseDevices,
      parkedDevices,
      offlineDevices
    };
  } catch (error) {
    console.error(error);
    throw new Error('Failed to get home data');
  }
};

const analyticsRide = async (req, res) => {
    try {
  
        const [ridesPerHour, ridesPerWeek, ridesPerMonth, ridesPerKm] = await Promise.all([
            api.getRidesPerHour(req),
            api.getRidesPerWeek(req),
            api.getRidesPerMonth(req),
            api.getRidesPerKm(req)
        ]);

        const ridesData = {
            ridesPerHour,
            ridesPerWeek,
            ridesPerMonth,
            ridesPerKm
        };

        res.render('analyticsRides', { ridesData });
    } catch (error) {
        console.error('Error loading analytics data:', error);
        res.status(500).render('analyticsRides', {
            ridesData: {
                ridesPerHour: [],
                ridesPerWeek: [],
                ridesPerMonth: [],
                ridesPerKm: [],
            },
            error: 'Could not load data.'
        });
    }
}


export default {login, init, map, mapRefresh, devices, addDevice, deleteDevice, analyticsRide};