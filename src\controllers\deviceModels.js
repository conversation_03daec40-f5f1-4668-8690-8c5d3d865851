import authenticate from '../middlewares/authenticate.js';
import deviceModel from '../models/deviceModels.js';
import LockStation from '../models/lockStations.js';

const newDeviceModel = async (req, res) => {
    try {
        const user = req.user;
        const new_deviceModel = req.body;

        if (!authenticate.hasAuthority("Tenant", user)) return res.status(400).send({ error: "Only tenant users are allowed to create device models" });
        if (!new_deviceModel.name) return res.status(400).send({ error: "No name provided" });
        if (!new_deviceModel.model) return res.status(400).send({ error: "No model provided" });
        if (!new_deviceModel.protocol) return res.status(400).send({ error: "No protocol provided" });

        new_deviceModel.tenantId = user.id;
        const model = await deviceModel.create(new_deviceModel);
        return res.status(200).json(model);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getDeviceModel = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (!id) return res.status(400).send({ error: "id not provided" });

        var model = await deviceModel.findByPk(id);
        if (!model) return res.status(404).send({ error: "Model not found" });

        if (user.type == "Customer") {
            if (model.tenantId != user.tenantId) return res.status(400).send({ error: "Model read not permitted" });
        }
        else if (user.type == 'Tenant') {
            if (model.tenantId != user.id) return res.status(400).send({ error: "Model read not permitted" });
        }

        return res.status(200).json(model);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getAllDevicesModels = async (req, res) => {
    try {
        const user = req.user;

        var models = {};
        if (authenticate.hasAuthority("Tenant", user)) {
            models = await deviceModel.findAll({
                where: {
                    tenantId: user.id,
                },
            });
        }
        else if (authenticate.hasAuthority("Customer", user)) {
            models = await deviceModel.findAll({
                where: {
                    tenantId: user.tenantId,
                },
            });
        }
        else if (authenticate.hasAuthority("Sysadmin", user)) {
            models = await deviceModel.findAll();
        }

        return res.status(200).json(models);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const updateDeviceModel = async (req, res) => {
    try {
        const user = req.user;
        const update_model = req.body;

        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to update models" });
        if (!update_model.id) return res.status(400).send({ error: "Model id not provided" });

        const Model = await deviceModel.findByPk(update_model.id);
        if (!Model) return res.status(404).send({ error: "Model not found" });
        if (Model.tenantId != user.id) return res.status(400).send({ error: "You are not the owner of this Model" });


        if (update_model.name) {
            Model.name = update_model.name;
        }
        if (update_model.model) {
            Model.model = update_model.model;
        }
        if (update_model.protocol) {
            Model.protocol = update_model.protocol;
        }
        if (update_model.description) {
            Model.description = update_model.description;
        }

        await Model.save();
        return res.status(200).json(Model);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const deleteDeviceModel = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (!id) return res.status(400).send({ error: "id not provided" });
        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to delete lock stations models" });

        var LockStations = await LockStation.findAll({
            where: {
                modelId: id
            }
        });
        if (!LockStations) {
            var model = await deviceModel.findByPk(id);
            if (!model) return res.status(404).send({ error: "Model not found" });
            if (model.tenantId != user.id) return res.status(400).send({ error: "Model delete not permitted" });
            await model.destroy();
            return res.status(200).send();
        }
        else return res.status(400).send({ error: "Can't delete model in use." })

    } catch (error) {
        res.status(500).json({ error: error });
    }
};
export default { newDeviceModel, getDeviceModel, deleteDeviceModel, updateDeviceModel, getAllDevicesModels };
