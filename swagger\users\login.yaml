paths:
# -------------> Login user <-------------
  "/users/login":
    post:
      security: []
      tags:
        - Users
      summary: Login User
      description: Login user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                  description: Username
                  example: "<EMAIL>"
                password:
                  type: string
                  description: Password
                  example: customer
      responses:
        200:
          description: Successful login
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Login successful
                  token:
                    type: string
                    description: JWT token
                    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  refreshToken:
                    type: string
                    description: Refresh token for generating new access tokens
                    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        400:
          description: Bad request
        404:
          description: Not found
        500:
          description: Internal server error

components:
  schemas:
    User:
      type: object
      required:
        - name
        - surname
        - username
        - password
        - mail
      properties:        
        name:
          type: string
          description: First name of the user
          example: Facundo
        surname:
          type: string
          description: Last name of the user
          example: Comasco
        username:
          type: string
          description: Unique username
          example: facucomasco
        password:
          type: string
          description: Password of the user
          example: abcdefg
        mail:
          type: string
          description: Email of the user
          example: "<EMAIL>"
