paths:
  # -------------> Add currency to Wallet <-------------
  "/users/wallet":
    post:
      security:
        - bearerAuth: []
      tags:
        - Users
      summary: Add currency to wallet
      description: "Add currency to wallet"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                amount:
                  type: integer
                  example: 1000
                  description: "amount to add to the wallet"
                token:
                  type: string
                  example: "17814497-a39f-4cc3-819f-4cc8c00d32df"
                  description: "token to authenticate the request from a microservice"
      responses:
        200:
          description: "Wallet"
        
     