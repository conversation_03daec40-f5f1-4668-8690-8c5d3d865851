import express from 'express';
import lockStationController from '../controllers/lockStation.js';
import validate from '../middlewares/validate.js';
import authenticate from '../middlewares/authenticate.js';

const router = express.Router();

router.post('/', authenticate.authenticateToken, lockStationController.newLockStation);
router.delete('/', authenticate.authenticateToken, lockStationController.deleteLockStation);
router.put('/', authenticate.authenticateToken, lockStationController.updateLockStation);
router.get('/', authenticate.authenticateToken, lockStationController.getLockStation);
router.get('/all', authenticate.authenticateToken, lockStationController.getAllLockStation);
router.put('/assign', authenticate.authenticateToken, lockStationController.assingToStation);
router.put('/unassign', authenticate.authenticateToken, lockStationController.unassignFromStation);
router.get('/lasttelemetry', authenticate.authenticateToken, lockStationController.getLastTelemetry);
router.get('/telemetries', authenticate.authenticateToken, lockStationController.getTelemetries);

export default router;