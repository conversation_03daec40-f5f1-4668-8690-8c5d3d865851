import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const Wallet = sequelize.define('wallet', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  amount: { type: DataTypes.INTEGER, allowNull: true },
  currency: {
    type: DataTypes.ENUM,
    values: ['FIXED', 'TIME', 'KM', 'WALLETFIX', 'WALLETTIME', 'WALLETKM'],
    allowNull: false,
    defaultValue: 'FIXED'
  },
});

export default Wallet;