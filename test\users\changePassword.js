import fetch from 'node-fetch';
import inputs from '../inputs.js';
import colors from '../colors.js';

export default async function changePassword(jwt) {
  let startTimestamp;
  let finishTimestamp;
  console.log();
  console.log(colors.bg.black + colors.fg.green + "#############################################" + colors.reset);
  console.log(colors.bg.black + colors.fg.green + 'START TEST => CHANGE PASSWORD' + colors.reset);
  console.log(colors.bg.black + colors.fg.green + "#############################################" + colors.reset);
  console.log();

  const endpoint = "/users/changepassword";
  const method = "POST";

  let Request = {
    password: inputs.password,
    newpassword: inputs.newpassword,
  };
  startTimestamp = Date.now();
  try {
    const response = await fetch('http://127.0.0.1:8080' + endpoint, {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwt}`
      },
      body: JSON.stringify(Request)
    });
    if (process.env.DEBUGRESPONSES === 'true') {
      console.log('Backend_Response:');
      console.log(response.status + " : " + response.statusText + " - " + await response.text())
      console.log();
    }
    finishTimestamp = Date.now();
    const TotalTime = finishTimestamp - startTimestamp;
    console.log('Response time: ' + TotalTime + " ms");

    if (response.status != 200) return false;
    else {
      Request = {
        newpassword: inputs.password,
        password: inputs.newpassword,
      };
      const response2 = await fetch('http://127.0.0.1:8080' + endpoint, {
        method: method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${jwt}`
        },
        body: JSON.stringify(Request)
      });
      if (response2.status != 200) return false;
      else return true;
    }
  } catch (error) {
    console.error('Error:', error);
    return false;
  }
}



