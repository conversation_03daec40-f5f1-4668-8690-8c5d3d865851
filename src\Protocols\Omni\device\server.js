import Device from '../../../models/device.js';
import TCPServer from '../../../servertcp.js';
import IoTMessageParser from './parser.js';
import OMNI from './process.js';
import IoTResponses from './responses.js';

const TCP_PORT = process.env.OMNI_PORT;
const TCP_HOST = '0.0.0.0';
const parser = new IoTMessageParser();
const Responses = new IoTResponses();
const server = new TCPServer(TCP_PORT, TCP_HOST);
const Timeout = 600000; //10 min
const connectedClients = new Map();

function socketTimeOut(socket) {
    if (socket.timer) clearTimeout(socket.timer);
    socket.timer = setTimeout(async function () {
        if (socket.imei) {
            const _device = await Device.findOne({
                where: { serial: socket.imei }
            });
            if (!_device) {
                console.log("Device not found");
                return;
            }
            if (_device.connected != false) {
                _device.connected = false;
                if (connectedClients.has(_device.serial)) {
                    connectedClients.delete(_device.serial);
                }
                await _device.save();
            }
            console.log("Connection with " + socket.imei + " closed for timeout");
        }
        socket.end();
    }, Timeout);
    return;
}

function SendData(data, socket) {
    server.send(socket, data);
}

server.on('ready', () => {
    console.log('OMNI server ready!');
});

server.on('connection', async (socket) => {
    const remoteAddress = socket.remoteAddress + ":" + socket.remotePort;
    console.log(`Nueva conexión desde: ${remoteAddress}`);
    try {
        socketTimeOut(socket);
    } catch (error) {
        return;
    }
});

server.on('data', async (socket, data) => {
    const remoteAddress = socket.remoteAddress + ":" + socket.remotePort;
    const receivedData = data.toString().trim();
    socketTimeOut(socket);
    try {
        //Parse
        const _data = parser.parse(receivedData);
        if (!_data) return;
        if (!_data.payload || _data.payload.error) return;

        //Found Device
        let _device = await Device.findOne({
            where: { serial: _data.imei }
        });
        if (!_device) {
            console.log("Device not found");
            return;
        }
        if (_device.connected != true) {
            _device.connected = true;
            await _device.save();
        }

        //Update socket status
        if (!socket.imei) {
            socket.imei = _data.imei;
        }
        //Update socket info
        if (connectedClients.has(_data.imei)) {
            const targetSocket = connectedClients.get(_data.imei);
            if (targetSocket != socket) {
                connectedClients.delete(_data.imei);
                connectedClients.set(_data.imei, socket);
            }
        }
        else {
            connectedClients.set(_data.imei, socket);
        }

        try {
            await OMNI.processMessages(_data, _device);
        } catch (error) {
            return;
        }

        const _response = await Responses.response(_data, _device);
        if (_response != undefined) server.send(socket, _response);
        return;
    } catch (error) {
        return;
    }
});

server.on('disconnect', async (socket) => {
    const remoteAddress = socket.remoteAddress + ":" + socket.remotePort;
    console.log(`Conexión cerrada: ${remoteAddress}`);
    if (socket.imei) {
        const _device = await Device.findOne({
            where: { serial: socket.imei }
        });
        if (!_device) {
            console.log("Device not found");
            return;
        }
        if (_device.connected != false) {
            _device.connected = false;
            if (connectedClients.has(_device.serial)) {
                connectedClients.delete(_device.serial);
            }
            await _device.save();
        }
        console.log("Connection with " + socket.imei + " closed on client side");
    }
    clearTimeout(socket.timer);
});

server.on('error', (socket, err) => {
    const remoteAddress = socket.remoteAddress + ":" + socket.remotePort;
    console.error(`Error en la conexión ${remoteAddress}:`, err.message);
});

server.on('serverError', (err) => {
    console.error('Error general del servidor TCP:', err.message);
    if (err.code === 'EADDRINUSE') {
        console.error("La dirección " + HOST + ":" + PORT + "ya está en uso.");
    }
});

server.on('close', () => {
    console.log('El servidor TCP se ha apagado completamente.');
});

server.start()
    .then(() => {
        console.log('Servidor iniciado con éxito.');
    })
    .catch(err => {
        console.error('No se pudo iniciar el servidor TCP:', err);
    });

process.on('SIGINT', () => {
    console.log('Detectada señal SIGINT. Cerrando servidor...');
    server.stop()
        .then(() => process.exit(0))
        .catch(err => {
            console.error('Error al cerrar el servidor por SIGINT:', err);
            process.exit(1);
        });
});

process.on('SIGTERM', () => {
    console.log('Detectada señal SIGTERM. Cerrando servidor...');
    server.stop()
        .then(() => process.exit(0))
        .catch(err => {
            console.error('Error al cerrar el servidor por SIGTERM:', err);
            process.exit(1);
        });
});
// }


export default { SendData, connectedClients, server };