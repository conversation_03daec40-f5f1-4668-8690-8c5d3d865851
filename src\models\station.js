import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const Station = sequelize.define('Station', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {type: DataTypes.STRING, allowNull: true},
  lat: { type: DataTypes.DOUBLE, allowNull: false },
  lon: { type: DataTypes.DOUBLE, allowNull: false },
  address: { type: DataTypes.STRING, allowNull: true },
  serial: { type: DataTypes.STRING, allowNull: true },
  protocol: { type: DataTypes.STRING, allowNull: false },
  spots: { type: DataTypes.INTEGER, allowNull: true, defaultValue: 0 },
  spots_availables: { type: DataTypes.INTEGER, allowNull: true, defaultValue: 0 },
  description: { type: DataTypes.STRING, allowNull: true }
});

export default Station;