
paths:
  # -------------> Delete Device Model by ID <-------------
  "/devices/models/":
    delete:
      tags:
        - Devices MODELS
      summary: Delete a device model by ID
      description: Deletes a device model using its unique ID.
      parameters:
        - name: id
          in: query
          description: Model Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
      responses:
        204:
          description: Device model deleted successfully
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error