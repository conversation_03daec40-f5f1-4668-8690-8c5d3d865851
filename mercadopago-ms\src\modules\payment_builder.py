import mercadopago
from mercadopago.config import RequestOptions

APP_DEEP_LINK_SCHEMA = "bikesharing://"
APP_DEEP_LINK_HOST = "mirgor.com"
APP_DEEP_LINK_PATH = "/mercadopago"
APP_DEEP_LINK = f"{APP_DEEP_LINK_SCHEMA}{APP_DEEP_LINK_HOST}{APP_DEEP_LINK_PATH}"


def build_payment_url(amount):
    request_options = RequestOptions(
        integrator_id="dev_24c65fb163bf11ea96500242ac130004"
    )
    sdk = mercadopago.SDK(
        "APP_USR-7683090044146734-120211-5d799b0a65d1446119f657ce35890ea7-2122472556",
        request_options=request_options,
    )

    preference_data = {
        "items": [
            {
                "id": "1234",
                "title": "<PERSON>gor",
                "currency_id": "ARS",
                "picture_url": "https://quint.com.ar/wp-content/uploads/2023/07/<EMAIL>",
                "description": "BikeSharing",
                "category_id": "art",
                "quantity": 1,
                "unit_price": amount,
            }
        ],
        "back_urls": {
            "success": f"{APP_DEEP_LINK}",
            "failure": f"{APP_DEEP_LINK}",
            "pending": f"{APP_DEEP_LINK}",
        },
        "payment_methods": {"excluded_payment_types": [{"id": "ticket"}]},
        "binary_mode": True,
        "auto_return": "approved",
        "statement_descriptor": "Mirgor BikeSharing",
        "external_reference": str(amount),
    }

    preference_response = sdk.preference().create(preference_data)
    preference = preference_response["response"]
    # payment_id = preference_response["response"]["id"]

    return preference
