import dotenv from 'dotenv';
import glob from 'glob';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
dotenv.config();

import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const yamlFiles = glob.sync(path.join(__dirname, '*/*.yaml'));
const options = {
  definition: {
    openapi: '3.0.3',
    info: {
      title: 'BikeSharing API - MIRGOR',
      description: "API endpoints for bikesharing system",
      contact: {
        name: "Facundo Comasco",
        email: "<EMAIL>",
      },
      version: '1.0.0',
    },
    servers: [
      {
        url: `https://develop.com.ar/`,
        description: "Develop server"
      },
      {
        url: `http://localhost:8080/`,
        description: "Local server"
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          in: "header",
          scheme: "bearer",
          bearerFormat: "JWT"
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ]
  },
  apis: yamlFiles
}


const swaggerSpec = swaggerJsdoc(options)
console.log('Cargando Swagger desde:', options.apis);
function swaggerDocs(app) {
  app.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec))
  app.get('/docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json')
    res.send(swaggerSpec)
  })
}
export default swaggerDocs
