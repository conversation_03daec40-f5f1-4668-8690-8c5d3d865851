import { DataTypes, Sequelize } from 'sequelize';
import sequelize from '../config/database.js';

const Telemetry = sequelize.define('Telemetry', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  userId: { type: DataTypes.INTEGER, allowNull: true },
  event: {
    type: DataTypes.ENUM,
    values: ['Unlock', 'Lock', 'Position', 'Turn', 'Connection', 'HeartBeat', 'UnlockRequest', 'LockRequest'],
    allowNull: true,
    defaultValue: 'Position'
  },
  voltage_battery: { type: DataTypes.DOUBLE, allowNull: true },
  Voltage_internal: { type: DataTypes.DOUBLE, allowNull: true },
  battery_level: { type: DataTypes.DOUBLE, allowNull: true },
  signal_strength: { type: DataTypes.INTEGER, allowNull: true },
  status: {
    type: DataTypes.ENUM,
    values: ['Locked', 'Unlocked', 'Unkown'],
    allowNull: true,
    defaultValue: 'Unkown'
  },
  charging: { type: DataTypes.BOOLEAN, allowNull: true },
  riding_Time: { type: DataTypes.INTEGER, allowNull: true },
  speedMode: {
    type: DataTypes.ENUM,
    values: ['Low', 'Medium', 'High', 'unset'],
    allowNull: true,
    defaultValue: 'unset'
  },
  speed: { type: DataTypes.INTEGER, allowNull: true },
  mileage: { type: DataTypes.INTEGER, allowNull: true },
  total_mileage: { type: DataTypes.INTEGER, allowNull: true },
  ts: { type: DataTypes.DATE, allowNull: true, defaultValue: Sequelize.NOW },
  lat: { type: DataTypes.DOUBLE, allowNull: true },
  lon: { type: DataTypes.DOUBLE, allowNull: true },
  satellites: { type: DataTypes.INTEGER, allowNull: true },
  HDOP: { type: DataTypes.DOUBLE, allowNull: true },
  Altitude: { type: DataTypes.INTEGER, allowNull: true },
});

export default Telemetry;