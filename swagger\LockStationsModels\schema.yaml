
components:
  schemas:
    LockStationModel:
      type: object
      required:
        - name
        - model
        - spots
        - protocol
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        tenantId:
          type: string
          format: uuid
          readOnly: true        
        name:
          type: string
          description: Name of the lock station model
          example: Omni 2
        model:
          type: string
          description: Lock Station's model
          example: OMNI 320
        spots:
          type: integer
          description: Number of spots for bikes
          example: 2
        protocol:
          type: string
          description: Lock station protocol of communication
          example: OMNI
        description:
          type: string
          description: Lock Station's description
          example: This lock station model lhas 2 spots for bikes.