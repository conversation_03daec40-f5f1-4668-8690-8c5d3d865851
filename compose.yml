services:
  app:
    hostname: bikesharing-server
    image: node:latest
    command: sh -c "apt update && apt install net-tools && npm i && npm run start"
    restart: unless-stopped
    env_file: ./.env
    ports:
      - 8080:8080
      - 8081:8081
      - 8095:8095
      - 8082:8082
      - 9229:9229
      - 8085:8085 #OMNI
      - 8086:8086 #OMNI debug
      - 8087:8087 #OMNI lock station
    expose:
      - 8082
    working_dir: /app
    volumes:
      - ./:/app
    stdin_open: true
    tty: true
    environment:
      - LETSENCRYPT_HOST=develop.com.ar
      - LETSENCRYPT_EMAIL=<EMAIL>
      - VIRTUAL_HOST=develop.com.ar
      - VIRTUAL_PORT=8080
    networks:
      - local_server_net


  pgadmin4:
    image: elestio/pgadmin:latest
    restart: always
    env_file: ./.env
    environment:
      PGADMIN_DEFAULT_EMAIL: $PGADMIN_DEFAULT_EMAIL
      PGADMIN_DEFAULT_PASSWORD: $PGADMIN_DEFAULT_PSW
      PGADMIN_LISTEN_PORT: $PGADMIN_DOCKER_PORT
    ports:
    - $PGADMIN_LOCAL_PORT:$PGADMIN_DOCKER_PORT
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - local_server_net

  postgresdb:
    image: postgres:latest
    restart: unless-stopped
    env_file: ./.env
    environment:
      - POSTGRES_USER=$POSTGRESDB_USER
      - POSTGRES_PASSWORD=$POSTGRESDB_ROOT_PASSWORD
      - POSTGRES_DB=$POSTGRESDB_NAME
    ports:
      - $POSTGRESDB_LOCAL_PORT:$POSTGRESDB_DOCKER_PORT
    volumes:
      - ./db:/var/lib/postgresql/data
    networks:
      - local_server_net

  nginx-proxy:
    image: jwilder/nginx-proxy:latest
    container_name: nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
      - ./nginx/certs:/etc/nginx/certs:ro
      - ./nginx/vhost.d:/etc/nginx/vhost.d
      - ./nginx/html:/usr/share/nginx/html
      - ./nginx/acme.sh:/etc/acme.sh
      - ./nginx/conf.d:/etc/nginx/conf.d
    restart: always
    logging:
      driver: "none"
    networks:
      - local_server_net

  letsencrypt:
    image: jrcs/letsencrypt-nginx-proxy-companion:latest
    container_name: letsencrypt
    environment:
      - NGINX_PROXY_CONTAINER=nginx-proxy
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./nginx/certs:/etc/nginx/certs
      - ./nginx/vhost.d:/etc/nginx/vhost.d
      - ./nginx/html:/usr/share/nginx/html
      - ./nginx/acme.sh:/etc/acme.sh
      - ./nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - nginx-proxy
    restart: always
    logging:
      driver: "none"
    networks:
      - local_server_net
  
  bind9:
    image: internetsystemsconsortium/bind9:9.18
    container_name: bind9
    ports:
      - "53:53/udp"  # Puerto DNS UDP
      - "53:53/tcp"  # Puerto DNS TCP
    volumes:
      - ./bind/config:/etc/bind
    restart: unless-stopped
    logging:
      driver: "none"
    networks:
      - local_server_net

  mercadopago-ms:
    hostname: mercadopago-ms
    build:
      context: .
      dockerfile: mercadopago-ms/Dockerfile
    networks:
      - local_server_net
    ports:
      - 8090:8090

  payway-ms:
    hostname: payway-ms
    build:
      context: .
      dockerfile: payway-ms/Dockerfile
    networks:
      - local_server_net
    ports:
      - 8091:8091

volumes:
  pgadmin_data:


networks:
  local_server_net:
    external: true