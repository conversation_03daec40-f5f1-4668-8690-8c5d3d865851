  # -------------> Get Device by serial <-------------
paths:
  "/devices/bySerial":
    get:
      tags:
        - Devices
      summary: Get a device by serial
      parameters:
        - name: serial
          in: query
          description: Device serial
          required: true
          schema:
            type: string
            example: "123456789123456"
      responses:
        200:
          description: Device found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error