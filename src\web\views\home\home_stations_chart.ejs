<div style="position: relative; height: 237px">
  <canvas id="home_stations_chart"></canvas>
  <div id="stations-spinner" style="
      position:absolute;
      top:0;
      left:0;
      width:100%;
      height:100%;
      display:flex;
      align-items:center;
      justify-content:center;
      ">
    <div class="spinner-border" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
</div>

<script>
  (function() {
    const chartEl = document.getElementById("home_stations_chart").getContext("2d");
    const spinnerEl = document.getElementById("stations-spinner");

    const onlineRGB = getRGBFromCSSVariable('--station');
    const offlineRGB = getRGBFromCSSVariable('--station-offline');
    const partialServiceRGB = getRGBFromCSSVariable('--station-partial-service');

    let stationsChart = null;

    function renderChart(onlineStations, offlineStations, partialServiceStations) {
      if (!stationsChart) {
        stationsChart = new Chart(chartEl, {
          type: "doughnut",
          data: {
            datasets: [{
              data: [onlineStations, offlineStations, partialServiceStations],
              backgroundColor: [onlineRGB, offlineRGB, partialServiceRGB],
            }],
            labels: ["Online", "Offline", "Partial service"],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            legend: {
              position: "right"
            },
            layout: {
              padding: {
                left: 20,
                right: 20,
                top: 20,
                bottom: 20
              }
            },
          },
        });
      } else {
        stationsChart.data.datasets[0].data = [onlineStations, offlineStations, partialServiceStations];
        stationsChart.update();
      }
      spinnerEl.style.display = "none"; // hide spinner when chart is ready
    }

    document.addEventListener("DOMContentLoaded", function() {
      window.App.subscribe(["onlineStations", "offlineStations", "partialServiceStations"], ({
        onlineStations,
        offlineStations,
        partialServiceStations
      }) => {
        if (onlineStations == null || offlineStations == null || partialServiceStations == null) {
          spinnerEl.style.display = "flex"; // show spinner if any value is null
          return;
        }
        renderChart(onlineStations, offlineStations, partialServiceStations);
      });
    });
  })();
</script>