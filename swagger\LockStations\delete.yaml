
paths:
  # -------------> Delete a Lock Station <-------------
  "/lockstations":
    delete:
      tags:
        - Lock Stations
      summary: Delete a Lock Station.
      description: Delete a Lock Station. Only tenants can delete Lock Station.
      parameters:
        - name: id
          in: query
          description: Lock Station Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
      responses:
        200:
          description: Lock Station deleted successfully
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error