paths:
  # -------------> Refresh <PERSON> <-------------
  "/users/refresh":
    post:
      tags:
        - Users
      summary: Refresh Access Token
      description: Generate a new access token using a refresh token.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                refreshToken:
                  type: string
                  description: Refresh token obtained during login
                  example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      responses:
        200:
          description: New access token generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  accessToken:
                    type: string
                    description: New JWT access token
                    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        400:
          description: Bad request
        401:
          description: Unauthorized
        500:
          description: Internal server error

components:
  schemas:
    User:
      type: object
      required:
        - name
        - surname
        - username
        - password
        - mail
      properties:        
        name:
          type: string
          description: First name of the user
          example: Facundo
        surname:
          type: string
          description: Last name of the user
          example: Comasco
        username:
          type: string
          description: Unique username
          example: facucomasco
        password:
          type: string
          description: Password of the user
          example: abcdefg
        mail:
          type: string
          description: Email of the user
          example: "<EMAIL>"
