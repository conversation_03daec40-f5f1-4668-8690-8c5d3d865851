<!DOCTYPE html>
<html lang="en">

<head>
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title>Google Maps - Kaiadmin Bootstrap 5 Admin Dashboard</title>
  <meta content="width=device-width, initial-scale=1.0, shrink-to-fit=no" name="viewport" />
  <link rel="icon" href="../assets/img/kaiadmin/favicon.ico" type="image/x-icon" />

  <!-- Fonts and icons -->
  <script src="../assets/js/plugin/webfont/webfont.min.js"></script>
  <script>
    WebFont.load({
      google: {
        families: ["Public Sans:300,400,500,600,700"]
      },
      custom: {
        families: [
          "Font Awesome 5 Solid",
          "Font Awesome 5 Regular",
          "Font Awesome 5 Brands",
          "simple-line-icons",
        ],
        urls: ["../assets/css/fonts.min.css"],
      },
      active: function() {
        sessionStorage.fonts = true;
      },
    });
  </script>

  <!-- CSS Files -->
  <link rel="stylesheet" href="../assets/css/bootstrap.min.css" />
  <link rel="stylesheet" href="../assets/css/plugins.min.css" />
  <link rel="stylesheet" href="../assets/css/kaiadmin.min.css" />
  <link rel="stylesheet" href="../static/css/home.css" />

  <!-- CSS Just for demo purpose, don't include it in your project -->
  <link rel="stylesheet" href="../assets/css/demo.css" />
  <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
  <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>

  <!-- Make sure Font Awesome is included -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />

  <!-- Include the MarkerClusterer library -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.1.0/dist/MarkerCluster.css" />
  <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.1.0/dist/MarkerCluster.Default.css" />
  <script src="https://unpkg.com/leaflet.markercluster@1.1.0/dist/leaflet.markercluster.js"></script>

  <!-- Chart JS -->
  <script src="../assets/js/plugin/chart.js/chart.min.js"></script>

  <!-- Home JS -->
  <script src="../static/js/home.js"></script>
</head>

<body>
  <div class="wrapper">

    <!-- Sidebar -->
    <%- include('partials/sidebar', { activePage: 'map', activeGroup: 'none' }) %>

    <div class="main-panel">
      <div class="main-header">
        <div class="main-header-logo">
          <!-- Logo Header -->
          <div class="logo-header" data-background-color="dark">
            <a href="../index.html" class="logo">
              <img src="../assets/img/kaiadmin/logo_light.svg" alt="navbar brand" class="navbar-brand" height="20" />
            </a>
            <div class="nav-toggle">
              <button class="btn btn-toggle toggle-sidebar">
                <i class="gg-menu-right"></i>
              </button>
              <button class="btn btn-toggle sidenav-toggler">
                <i class="gg-menu-left"></i>
              </button>
            </div>
            <button class="topbar-toggler more">
              <i class="gg-more-vertical-alt"></i>
            </button>
          </div>
          <!-- End Logo Header -->
        </div>

        <!-- Navbar Header -->
        <%- include('partials/navbar') %>

      </div>

      <div class="container">
        <div class="page-inner">
          <%- include('home/home_all_top_cards') %>

          <div class="row">
            <div class="col-md-8">
              <div class="card">
                <div class="card-header">
                  <div class="d-flex justify-content-between align-items-center">
                    <div class="card-title mb-0">Map view</div>
                    <%- include('home/home_map_switches') %>
                  </div>
                </div>
                <div class="card-body">
                  <%- include('home/home_map') %>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card">
                <div class="card-header">
                  <div class="card-title">Stations</div>
                </div>
                <div class="card-body">
                  <%- include('home/home_stations_chart') %>
                </div>
              </div>
              <div class="card">
                <div class="card-header">
                  <div class="card-title">Vehicles</div>
                </div>
                <div class="card-body">
                  <%- include('home/home_vehicles_chart') %>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <%- include('partials/footer') %>

    </div>

    <!-- Settings -->
    <%- include('partials/settings') %>

  </div>

  <script>
    (function() {
      window.App = {
        bus: new EventTarget(),
        EVENT_NAME: 'state:update',
        state: {
          stations: null,
          devices: null,
          ridingDevices: null,
          totalUsers: null,
          onlineStations: null,
          offlineStations: null,
          partialServiceStations: null,
          inUseDevices: null,
          parkedDevices: null,
          offlineDevices: null,
        },
        subscribe(keys, handler) {
          // Accept either a single key or an array of keys
          if (!Array.isArray(keys)) keys = [keys];

          const wrapper = (e) => {
            const payload = e.detail;
            const changed = {};
            let hasChange = false;

            keys.forEach(key => {
              if (key in payload) {
                changed[key] = payload[key];
                hasChange = true;
              }
            });

            if (hasChange) handler(changed);
          };

          this.bus.addEventListener(this.EVENT_NAME, wrapper);
          return () => this.bus.removeEventListener(this.EVENT_NAME, wrapper);
        },
        setState(newState) {
          this.state = {
            ...this.state,
            ...newState
          };
          this.bus.dispatchEvent(new CustomEvent(this.EVENT_NAME, {
            detail: this.state
          }));
        }
      };
    })();
  </script>

  <script>
    const POLL_MS = 1000;

    (function() {
      async function refreshState() {
        const res = await fetch('/map/refresh');
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        const data = await res.json();
        window.App.setState(data);
      }

      document.addEventListener('DOMContentLoaded', () => {
        refreshState();
        setInterval(refreshState, POLL_MS);
      });
    })();
  </script>


  <!--   Core JS Files   -->
  <script src="../assets/js/core/jquery-3.7.1.min.js"></script>
  <script src="../assets/js/core/popper.min.js"></script>
  <script src="../assets/js/core/bootstrap.min.js"></script>

  <!-- jQuery Scrollbar -->
  <script src="../assets/js/plugin/jquery-scrollbar/jquery.scrollbar.min.js"></script>
  <!-- Kaiadmin JS -->
  <script src="../assets/js/kaiadmin.min.js"></script>
  <!-- Kaiadmin DEMO methods, don't include it in your project! -->
  <script src="../assets/js/setting-demo2.js"></script>
</body>

</html>