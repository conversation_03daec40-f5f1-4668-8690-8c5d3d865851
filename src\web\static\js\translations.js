// translations.js
const translations = {
    es: {
        label: "SISTEMA DE RENTA",
      ingresar: "INGRESAR",
      usuario: "USUAR<PERSON>",
      password: "CONTRASEÑA",
      bad_credencials: "Revise las credenciales",
      ingreso: "INGRESO",
      olvidastePassword: "Olvidaste el password?",
      oauth: `O ingresa con: `,
      titulo: "SISTEMA DE ALQUILER DE BICICLETAS",
      inicio: "INICIO",
      dispositivos: "DISPOSITIVOS",
      usuarios: "USUARIOS",
      graficos: "GRAFICOS",
      contactanos: "CONTACTANOS",
      historial: "HISTORIAL",
      salir: "SALIR",
      perfil: "PERFIL",
      estaciones: "ESTACIONES"
    },
    en: {
        label: "BIKE RENTAL",
      ingresar: "ENTER",
      usuario: "USERNAME",
      password: "PASSWORD",
      bad_credencials: "Check your credentials",
      ingreso: "LOGI<PERSON>",
      olvidastePassword: "Forget your password?",
      oauth: "Or login with: ",
      titulo: "BIKE RENTAL SYSTEM",
      inicio: "HOME",
      dispositivos: "DEVICES",
      usuarios: "USERS",
      graficos: "GRAPHS",
      contactanos: "CONTACT US",
      historial: "HISTORY",
      salir: "LOG OUT",
      perfil: "PROFILE",
      estaciones: "STATIONS"
      // Agrega más traducciones aquí
    },
  };

  function traducir(){
    const langToggleBtn = document.getElementById("lang-toggle");
    let currentLang = localStorage.getItem("lang") || "es"; // Obtener idioma guardado o predeterminar a 'es'

    function applyTranslations() {
        const elementsToTranslate = document.querySelectorAll("[data-translate-key]");
        elementsToTranslate.forEach(element => {
            const key = element.getAttribute("data-translate-key");
            if (translations[currentLang] && translations[currentLang][key]) {
                element.textContent = translations[currentLang][key];                
            }
        });
        // Actualizar el texto del botón de cambio de idioma
        if (currentLang === "es") {
            langToggleBtn.classList.remove("eeuu");
            langToggleBtn.classList.add("arg");
            // langToggleBtn.textContent = "English";
        } else {
            langToggleBtn.classList.remove("arg");
            langToggleBtn.classList.add("eeuu");
            // langToggleBtn.textContent = "Español";
        }
    }
    function applyTranslationsPlaceHolders() {
        const elementsToTranslate = document.querySelectorAll("[data-translate-key-placeholder]");
        elementsToTranslate.forEach(element => {
            const key = element.getAttribute("data-translate-key-placeholder");
            if (translations[currentLang] && translations[currentLang][key]) {
                element.placeholder = translations[currentLang][key];                
            }
        });
        // Actualizar el texto del botón de cambio de idioma
        // if (currentLang === "es") {
        //     langToggleBtn.textContent = "English";
        // } else {
        //     langToggleBtn.textContent = "Español";
        // }
    }

    // Cambiar idioma al hacer clic en el botón
    langToggleBtn.addEventListener("click", () => {
        currentLang = currentLang === "es" ? "en" : "es";
        localStorage.setItem("lang", currentLang); // Guardar el idioma seleccionado
        document.documentElement.lang = currentLang; // Actualizar el atributo lang del <html>
        applyTranslations();
        applyTranslationsPlaceHolders();
    });

    // Aplicar traducciones al cargar la página
    applyTranslations();
    applyTranslationsPlaceHolders();
    document.documentElement.lang = currentLang; // Asegurarse de que el atributo lang sea correcto al inicio
}
document.addEventListener("DOMContentLoaded", () => {
   traducir();
});
