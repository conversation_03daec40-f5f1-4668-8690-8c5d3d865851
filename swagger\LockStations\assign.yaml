
paths:
  # -------------> Assign Lock Station <-------------
  "/lockstations/assign":
    put:
      tags:
        - Lock Stations
      summary: Assign Lock Station to a station.
      description: Assign Lock Station to a Station.
      parameters:
        - name: stationId
          in: query
          description: Station Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
        - name: lockStationId
          in: query
          description: Lock Station Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"

      responses:
        200:
          description: Lock Station assigned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LockStation'
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error