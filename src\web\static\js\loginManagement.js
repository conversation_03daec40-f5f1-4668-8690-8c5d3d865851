document.addEventListener('keydown', function (e) {
    if (e.key === 'Enter') {
      handlesubmit();
    }
  });

async function validateOldToken(){
    const token = localStorage.getItem('accessToken');
    if(!token)return;
    const response = await fetch('/stations/all', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
    });
    
    if (!response.ok) {
        // document.getElementById("login_bad_credentials").style.display = "block";
        return;
    }
        const status = response.status;
        //login ok
        if (status == 200) {
            document.cookie = `accessToken=${token}; path=/;`;
            window.location.href = '/map';
    }
}
validateOldToken();
  
async function handlesubmit() {
    try {
        document.getElementById("login_bad_credentials").style.color = "transparent";
        //comprobacion de los campos de login
        if(document.getElementById("login_username").value == "")throw new Error('Ingrese un usuario');
        if(document.getElementById("login_password").value == "")throw new Error('Ingrese un password');
        const response = await fetch('/users/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: document.getElementById("login_username").value,
                password: document.getElementById("login_password").value
            })
        });

        if (!response.ok) {
            document.getElementById("login_bad_credentials").style.color = "red";
            return;
            throw new Error('Login fallido');
        }
        const status = response.status;
        //login ok
        if(status==200){
            // alert("Login correcto");
            const data = await response.json();
            // alert('Respuesta del servidor: ' + data.accessToken);
            // Guardar en localStorage
            localStorage.setItem('accessToken', data.accessToken);
            document.cookie = `accessToken=${data.accessToken}; path=/;`;
            localStorage.setItem('refreshToken', data.refreshToken);
            window.location.href = '/map';
        }
        else {
            const data = await response.json();
            alert("Login incorrecto. "+ response.error);
            document.getElementById("login_bad_credentials").style.color = "red";
        }
        return;
    } catch (error) {
        console.error('Error:', error);
        alert('Error: ' + error.message);
        return null;
    }
}

function logout() {
    localStorage.removeItem('accessToken');
    window.location.href = '/';
}

