import authenticate from '../middlewares/authenticate.js';
import LockStation from '../models/lockStations.js';
import lockStationModel from '../models/lockStationsModels.js';

const newLockStationModel = async (req, res) => {
    try {
        const user = req.user;
        const new_lockstationmodel = req.body;

        if (!authenticate.hasAuthority("Tenant", user)) return res.status(400).send({ error: "Only tenant users are allowed to create lock station models" });
        if (!new_lockstationmodel.name) return res.status(400).send({ error: "No name provided" });
        if (!new_lockstationmodel.model) return res.status(400).send({ error: "No model provided" });
        if (!new_lockstationmodel.spots) return res.status(400).send({ error: "No spots number provided" });
        if (!new_lockstationmodel.protocol) return res.status(400).send({ error: "No protocol provided" });

        new_lockstationmodel.tenantId = user.id;
        const newStation = await lockStationModel.create(new_lockstationmodel);
        return res.status(200).json(newStation);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getLockStationModel = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (!id) return res.status(400).send({ error: "id not provided" });

        var Model = await lockStationModel.findByPk(id);
        if (!Model) return res.status(404).send({ error: "Model not found" });

        if (user.type == "Customer") {
            if (Model.tenantId != user.tenantId) return res.status(400).send({ error: "Model read not permitted" });
        }
        else if (user.type == 'Tenant') {
            if (Model.tenantId != user.id) return res.status(400).send({ error: "Model read not permitted" });
        }

        return res.status(200).json(Model);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getAllLockStationModels = async (req, res) => {
    try {
        const user = req.user;

        var models = {};
        if (authenticate.hasAuthority("Tenant", user)) {
            models = await lockStationModel.findAll({
                where: {
                    tenantId: user.id,
                },
            });
        }
        else if (authenticate.hasAuthority("Customer", user)) {
            models = await lockStationModel.findAll({
                where: {
                    tenantId: user.tenantId,
                },
            });
        }
        else if (authenticate.hasAuthority("Sysadmin", user)) {
            models = await lockStationModel.findAll();
        }

        return res.status(200).json(models);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const updateLockStationModel = async (req, res) => {
    try {
        const user = req.user;
        const update_model = req.body;

        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to update models" });
        if (!update_model.id) return res.status(400).send({ error: "Model id not provided" });

        const Model = await lockStationModel.findByPk(update_model.id);
        if (!Model) return res.status(404).send({ error: "Model not found" });
        if (Model.tenantId != user.id) return res.status(400).send({ error: "You are not the owner of this Model" });

        if (update_model.name) {
            Model.name = update_model.name;
        }
        if (update_model.model) {
            Model.model = update_model.model;
        }
        if (update_model.spots) {
            Model.spots = update_model.spots;
        }
        if (update_model.protocol) {
            Model.protocol = update_model.protocol;
        }
        if (update_model.description) {
            Model.description = update_model.description;
        }

        await Model.save();
        return res.status(200).json(Model);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const deleteLockStationModel = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;
        
        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to delete lock stations models" });
        if (!id) return res.status(400).send({ error: "id not provided" });

        var LockStations = await LockStation.findAll({
            where: {
                modelId: id
            }
        });
        if (!LockStations) {
            var model = await lockStationModel.findByPk(id);
            if (!model) return res.status(404).send({ error: "Model not found" });
            if (model.tenantId != user.id) return res.status(400).send({ error: "Model delete not permitted" });
            await model.destroy();
            return res.status(200).send();
        }
        else return res.status(400).send({ error: "Can't delete model in use." })

    } catch (error) {
        res.status(500).json({ error: error });
    }
};

export default { newLockStationModel, getLockStationModel, deleteLockStationModel, updateLockStationModel, getAllLockStationModels };
