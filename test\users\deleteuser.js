import fetch from 'node-fetch';
import inputs from '../inputs.js';

export default async function deleteUser() {
  let startTimestamp;
  let finishTimestamp;
  console.log();
  console.log("#############################################");
  console.log('START TEST => DELETE USER');
  console.log("#############################################");
  console.log();

  const endpoint = "/users";
  const method = "DELETE";

  let Request = {
    name: inputs.name,
    surname: inputs.surname,
    username: inputs.username,
    password: inputs.password,
    mail: inputs.mail,
    tenantId: inputs.tenantId,
  };
  startTimestamp = Date.now();
  console.log('> STARTING REQUEST');
  console.log();

  try {
    const response = await fetch('http://127.0.0.1:8080' + endpoint, {
      method: method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(Request)
    });
    console.log('-----------------------------------------------------------');
    console.log('Backend_Response:');
    console.log(response.status + " : " + response.statusText + " - " + await response.text())
    console.log();
    finishTimestamp = Date.now();
    const TotalTime = finishTimestamp - startTimestamp;
    console.log('Response time: ' + TotalTime + " mili segundos");

    console.log("#############################################");
    console.log('END CREATE USER WITH SAME USERNAME TEST');
    console.log("#############################################");
    if (response.status != 400) return false;
    else return true;
  } catch (error) {
    console.error('Error:', error);
    return false;
  }
}



