import Configuration from "../../../models/configuration.js";
import Device from "../../../models/device.js";
import Rents from "../../../models/rents.js";
import Telemetry from "../../../models/telemetry.js";
import User from "../../../models/users.js";
import IoTResponses from "../device/responses.js";
import server from "../device/server.js";

async function processMessages(data, lockStation) {
    try {
        switch (data.commandType) {
            case 'SD':
                await processSD(data, lockStation);
                break;
            case 'Q0', 'H0':
                break;
            default:
                break;
        }
        // The charging station status is reported separately
        if ('SD' !== data.commandType) {
            await addTelemetry(data.payload, lockStation);
        }
        return { code: 200 };
    } catch (error) {
        return { code: 401, error: error };
    }
}

async function processSD(data, lockStation) {
    const payload = data.payload;
    const statusLockStation1 = payload.statusLockStation1;
    const statusLockStation2 = payload.statusLockStation2;
    var devices = await lockStation.getRelatedDevices();
    await checkDeviceReturned(lockStation, statusLockStation1, 1, devices);
    await checkDeviceReturned(lockStation, statusLockStation2, 2, devices);
}

async function checkDeviceReturned(lockStation, statusLockStation, lockStationIndex, devices) {
    if (!statusLockStation.deviceImei) {
        return;
    }
    if ('Unlocking' == lockStation.spotsStatus[lockStationIndex]) {
        if (statusLockStation.locked) {
            // This prevents to report the device as returned to the station when it sends an old lock status message
            return;
        }
        // It is necessary to be detected as a change in sequelize
        let spotsStatus = { ...lockStation.spotsStatus };
        spotsStatus[lockStationIndex] = 'Unlocked';
        lockStation.spotsStatus = spotsStatus;
        await lockStation.save();
    }
    if (!statusLockStation.locked) {
        return;
    }
    if (devices.some(device => device.serial === statusLockStation.deviceImei)) {
        // The device is already assigned to the lock station
        return;
    }
    var device = await Device.findOne({
        where: { serial: statusLockStation.deviceImei },
    });
    if (!device) {
        console.error("Unknown device detected in a lock station: " + statusLockStation.deviceImei);
        return;
    }
    if (device.LockStationId) {
        console.error("A device was detected in a lock station when it was already assigned to a previous lock station. " +
            "Previous lock station: " + device.LockStationId +
            "Requested lock station: " + lockStation.id +
            "Device: " + statusLockStation.deviceImei);
        return;
    }
    await returnDeviceToStation(lockStation, lockStationIndex, device);
}

async function returnDeviceToStation(lockStation, lockStationIndex, device) {
    var lockStationModel = await lockStation.getRelatedModel();
    if (lockStationIndex > lockStationModel.spots) {
        console.error("Invalid lock station index");
        return;
    }
    var deviceModel = await device.getRelatedModel();
    if (lockStationModel.protocol != deviceModel.protocol) {
        console.error("The device is not compatible with the lock station");
        return;
    }

    var station = await lockStation.getRelatedStation();
    if (!station) {
        console.error("Failed to return device to station. Lock station has no station: " + lockStation.id);
        return;
    }

    var customerId = device.customerId;

    station.spots_availables = station.spots_availables - 1;

    device.customerId = null;
    device.LockStationId = lockStation.id;
    device.stationIndex = lockStation.stationStartIndex + lockStationIndex - 1; // Starts at 1
    device.status = "Ready";
    
    await device.save();
    await station.save();

    await saveReturnDeviceTelemetry(station, lockStation, device);

    const rent = await Rents.findOne({
        where: {deviceId: device.id},
        order: [['createdAt', 'DESC']],
        limit: 1,
        separate: true, // It can avoid problems in limiting and ordering
    });
    if (!rent) {
        console.log("Failed to get the rent data from the device returned");
        return;
    }
    const configuration = await Configuration.findOne({ where: { tenantId: device.tenantId } });
    if (!configuration) {
        console.log("Failed to get the configuration data from the tenant of the device returned");
        return;
    }
    const user = await User.findByPk(customerId);
    if (!user) {
        console.log("Failed to get the user from the device returned");
        return;
    }

    const costPerMinute = configuration.price;
    const endTime = Date.now();
    const totalTimeSeconds = Math.floor((endTime - rent.startTime) / 1000);
    const totalTimeMinutes = Math.ceil(totalTimeSeconds / 60); // Round up to the nearest minute
    var price = totalTimeMinutes * costPerMinute;
    price = parseFloat(price.toFixed(2)); // Round to 2 decimals

    rent.endStation = station.id;
    rent.endTime = endTime;
    rent.totalTime = totalTimeSeconds;
    rent.price = price;
    await rent.save();

    user.credit -= price;
    await user.save();

    const lockUnlockKey = device.locking_parameters.Key;
    if (!lockUnlockKey) {
        console.log("No lock/unlock key available to lock the device");
        return;
    }
    if (!server.connectedClients.has(device.serial)) {
        console.log("The device is not connected to the server. Unable to send the lock command");
        return;
    }
    const Responses = new IoTResponses();
    let lockMessage = Responses.buildMessage(device.serial, "L1", [lockUnlockKey])

    const targetSocket = server.connectedClients.get(device.serial);
    await server.server.send(targetSocket, lockMessage);
}

async function saveReturnDeviceTelemetry(station, lockStation, device) {
    await addReturnDeviceTelemetry(
        { "event": "Lock" },
        station,
        lockStation,
        device,
    );
}

async function addTelemetry(payload, lockStation) {
    var telemetry = {}
    var lastTelemetry = await Telemetry.findOne(
        {
            where: { lockStationId: lockStation.id },
            order: [['updatedAt', 'DESC']]
        }
    )
    if (lastTelemetry) {
        telemetry = { ...lastTelemetry.dataValues, ...payload };
        delete telemetry.id;
        delete telemetry.createdAt;
        delete telemetry.updatedAt;
    }
    else {
        telemetry = payload;
    }
    telemetry.ts = Date.now();
    telemetry.lockStationId = lockStation.id;

    await Telemetry.create(telemetry).catch(function (err) {
        return { code: 401, error: err };
    });
}

async function addReturnDeviceTelemetry(payload, station, lockStation, device) {
    var telemetry = {}
    var lastTelemetry = await Telemetry.findOne(
        {
            where: { lockStationId: lockStation.id },
            order: [['updatedAt', 'DESC']]
        }
    )
    if (lastTelemetry) {
        telemetry = { ...lastTelemetry.dataValues, ...payload };
        delete telemetry.id;
        delete telemetry.createdAt;
        delete telemetry.updatedAt;
    }
    else {
        telemetry = payload;
    }
    telemetry.ts = Date.now();
    telemetry.lockStationId = lockStation.id;
    telemetry.deviceId = device.id;
    telemetry.StationId = station.id;

    await Telemetry.create(telemetry).catch(function (err) {
        return { code: 401, error: err };
    });
}

export default { processMessages };
