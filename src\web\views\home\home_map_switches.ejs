<div class="d-flex gap-5">
  <div class="form-check form-switch d-flex align-items-center m-0 p-0">
    <label for="stationsSwitch" class="form-check-label me-4 mb-0" style="line-height:1; margin-top:2px;">
      Stations
    </label>
    <input id="stationsSwitch" class="form-check-input" type="checkbox" role="switch" disabled checked
        style="transform: scale(1.5); transform-origin: center;">
  </div>

  <div class="form-check form-switch d-flex align-items-center m-0 p-0">
    <label for="devicesSwitch" class="form-check-label me-4 mb-0" style="line-height:1; margin-top:2px;">
      Bikes
    </label>
    <input id="devicesSwitch" class="form-check-input" type="checkbox" role="switch" disabled checked
        style="transform: scale(1.5); transform-origin: center;">
  </div>
</div>

<script>
  function setSwitchKnobColor(switchId, cssVarName) {
    const rootStyle = getComputedStyle(document.documentElement);
    const color = rootStyle.getPropertyValue(cssVarName).trim();
    if (!color) return;

    const svgUnchecked =
      `<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='${color}'/></svg>`;
    const svgChecked =
      `<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#ffffff'/></svg>`;

    const dataUnchecked = 'data:image/svg+xml,' + encodeURIComponent(svgUnchecked);
    const dataChecked = 'data:image/svg+xml,' + encodeURIComponent(svgChecked);

    const valueUnchecked = `url("${dataUnchecked}")`;
    const valueChecked = `url("${dataChecked}")`;

    const styleId = `switch-style-${switchId}`;
    let styleEl = document.getElementById(styleId);
    if (!styleEl) {
      styleEl = document.createElement('style');
      styleEl.id = styleId;
      document.head.appendChild(styleEl);
    }

    styleEl.textContent = `
      #${switchId}.form-check-input {
        --bs-form-switch-bg: ${valueUnchecked};
        --bs-form-switch-checked-bg: ${color};
        --bs-form-switch-checked-border-color: ${color};
      }
      #${switchId}.form-check-input:checked {
        --bs-form-switch-bg: ${valueChecked};
      }
    `;
  }

  document.addEventListener('DOMContentLoaded', () => {
    setSwitchKnobColor('stationsSwitch', '--station');
    setSwitchKnobColor('devicesSwitch', '--bike');

    window.App.subscribe('stations', ({
      stations
    }) => {
      const stationsSwitch = document.getElementById('stationsSwitch');
      stationsSwitch.disabled = (stations === null) ? true : false;
    });

    window.App.subscribe('devices', ({
      devices
    }) => {
      const devicesSwitch = document.getElementById('devicesSwitch');
      devicesSwitch.disabled = (devices === null) ? true : false;
    });
  });
</script>