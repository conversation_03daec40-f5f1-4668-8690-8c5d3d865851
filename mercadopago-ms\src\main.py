import logging

import modules.payment_builder as pay
from flask import Flask, jsonify, request
from modules.webhook import Webhook

app = Flask(__name__)


@app.route("/smartlink/<path:resource>")
def smart_link(resource):
    # Replace with your app's deep link schema (e.g., myapp://details/<resource>)
    deep_link = f"com.mirgor.bikesharing://{resource}"

    # Return HTML with a script for smarter handling
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Open MyApp</title>
    </head>
    <body>
        <p>Redirecting you to the app...</p>
        <script>
            // Attempt to open the app
            window.location = "{deep_link}";
        </script>
    </body>
    </html>
    """


@app.route("/", methods=["POST"])
def catch_webhook_message():
    try:
        WEBHOOK_TOKEN = (
            "7f5a0b4588bfedd9235b8eb64b881d5b2e9fb995dc0122248debcac47ecc51bb"
        )

        mercadopago_webhook = Webhook(token=WEBHOOK_TOKEN)

        print(type(request))

        if mercadopago_webhook.validate_webhook(incomming_request=request):
            return jsonify({"status": "OK"}), 200
        return jsonify({"status": "HMAC verification failed"}), 400

    except Exception as e:
        return jsonify({"error": f"Something went wrong : {e}"}), 500


@app.route("/payment", methods=["POST"])
def get_payment_url():
    amount = request.json["amount"]
    if amount is None:
        return jsonify({"message": "missing amount"}), 400

    try:
        data = pay.build_payment_url(amount)
        if "init_point" in data.keys():
            return jsonify({"payment_url": data["init_point"], "data": data}), 200
        return jsonify({"status": "error generating the payment url"}), 400
    except Exception as e:
        return jsonify({"error": f"Something went wrong : {e}"}), 500


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    app.run(debug=True, port=8090, host="0.0.0.0", ssl_context="adhoc")
