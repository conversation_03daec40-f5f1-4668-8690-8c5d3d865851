
paths:
  # -------------> Delete station <-------------
  "/stations":
    delete:
      tags:
        - Stations
      summary: Delete a station.
      description: Delete a station. Only tenants can delete stations.
      parameters:
        - name: id
          in: query
          description: Station Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
      responses:
        200:
          description: Station deleted successfully
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error