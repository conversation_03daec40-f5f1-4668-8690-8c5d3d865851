import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const LockStation = sequelize.define('LockStations', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  serial: { type: DataTypes.STRING, allowNull: false , unique:true},
  phone: { type: DataTypes.INTEGER, allowNull: false, unique:true },
  description: { type: DataTypes.STRING, allowNull: true },
  connected: { type: DataTypes.BOOLEAN, allowNull: true },
  stationStartIndex: {type: DataTypes.INTEGER, allowNull: false, unique: false },
  spotsStatus: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {},
    get() {
      return this.getDataValue("spotsStatus");
    },
    set(value) {
      return this.setDataValue("spotsStatus", value);
    }
  },
});

export default LockStation;