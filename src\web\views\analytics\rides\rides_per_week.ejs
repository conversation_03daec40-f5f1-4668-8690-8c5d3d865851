<!--Bar chart: Number of rides per week (Divided by day)-->
<canvas id="ridesPerWeekChart" width="400" height="200"></canvas>

<div class="mt-3">
  <p><strong>Total rides this week: <span id="totalRidesWeek">0</span></strong></p>
</div>

<script>
  function createRidesPerWeekChart() {
    const ctx = document.getElementById('ridesPerWeekChart').getContext('2d');

    const ridesPerWeekData = ridesData?.ridesPerWeek || [];

    const days = ridesPerWeekData.map(item => item.dayOfWeek);
    const counts = ridesPerWeekData.map(item => item.count);

    const totalRides = counts.reduce((sum, count) => sum + count, 0);
    document.getElementById('totalRidesWeek').textContent = totalRides;

    new Chart(ctx, {
      type: 'bar',
      data: {
        labels: days,
        datasets: [{
          label: 'Rides per Day',
          data: counts,
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 205, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)',
            'rgba(199, 199, 199, 0.6)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 205, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(199, 199, 199, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Rides'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Day of Week'
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          title: {
            display: true,
            text: 'Rides Distribution by Day of Week'
          }
        }
      }
    });
  }
</script>