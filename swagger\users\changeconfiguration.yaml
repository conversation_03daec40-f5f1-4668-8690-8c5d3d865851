paths:
  # -------------> Ref<PERSON> <-------------
  "/users/configuration":
    post:
      security:
        - bearerAuth: []
      tags:
        - Users
      summary: Configure payment method
      description: Configure payment method, FIXED, TIME, KM, <PERSON><PERSON><PERSON>FIXED, <PERSON>LLETTIME or WALLETKM.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                price:
                  type: integer
                  example: 2500
                  description: "FIXED: unlock price, TIME: Minutes, km, WALLET da la posibilidad de comprar anticipado"
                paymentMethod:
                  type: string
                  example: TIME
                  description: Payment Method (FIXED, TIME, KM, <PERSON><PERSON><PERSON>FIXED, WALLETTIME,WALLETKM)

      responses:
        200:
          description: Configuration changed successfully
        400:
          description: Bad request
        401:
          description: Unauthorized
        500:
          description: Internal server error

components:
  schemas:
    User:
      type: object
      required:
        - name
        - surname
        - username
        - password
        - mail
      properties:        
        name:
          type: string
          description: First name of the user
          example: Facundo
        surname:
          type: string
          description: Last name of the user
          example: Comasco
        username:
          type: string
          description: Unique username
          example: facucomasco
        password:
          type: string
          description: Password of the user
          example: abcdefg
        mail:
          type: string
          description: Email of the user
          example: "<EMAIL>"
