paths:
  "/devices/rent/rides-per-hour":
    get:
      tags:
        - Rent
      summary: Get number of rides per hour, considering start time 
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: startTime
          schema:
            type: integer
            description: Start time as a UNIX timestamp in milliseconds
            example: 1758555994917
        - in: query
          name: endTime
          schema:
            type: integer
            description: End time as a UNIX timestamp in milliseconds
            example: 1758559994917
      responses:
        200:
          description: Rents found
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        408:
          description: Request timeout
        500:
          description: Internal server error