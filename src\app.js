import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';


const app = express();
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Set EJS as the view engine
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'web/views'));

// Serve static assets
app.use('/assets', express.static(path.join(__dirname, 'web/assets')));
app.use('/static', express.static(path.join(__dirname, 'web/static')));

// Use cookies for login (this might be better to later replace with backend session)
import cookieParser from 'cookie-parser';
app.use(cookieParser());

// Route for homepage
import webRoutes from './web/routes/webRoutes.js';
app.use('/', webRoutes)


// Swagger and API routes
import swaggerDocs from '../swagger/swagger_config.js';
import devicesRoutes from './routes/device.js';
import devicesModelsRoutes from './routes/deviceModels.js';
import lockStations from './routes/lockStations.js';
import lockStationsModels from './routes/lockStationsModels.js';
import rentsRoutes from './routes/rent.js';
import stationsRoutes from './routes/station.js';
import usersRoutes from './routes/user.js';

//Swagger
swaggerDocs(app);

//API Routes
app.use('/devices', devicesRoutes);
app.use('/devices/rent', rentsRoutes);
app.use('/stations', stationsRoutes);
app.use('/users', usersRoutes);
app.use('/lockstations/models', lockStationsModels);
app.use('/lockstations', lockStations);
app.use('/devices/models',devicesModelsRoutes);

export default app;