body {
    background-color: #1d2431;
    size: 100%;
    width: auto;
    height: 99vh;
    overflow: hidden;
    font-family: "Helvetica Neue", Helvetica, sans-serif;
    color: white;
}
.bad_credentials{
    color: transparent;
    margin-top: 16px;
    margin-bottom: 2px;
}
main {
    margin: 4px 0 0px 0;
    background-color: #1d2431;
    height: 99vh;
    padding-bottom: 100px;
    overflow: hidden;
}

.map {
    width: calc(100vw);
    left: 250px;
    height: 100vh;
    background: radial-gradient(circle, #ffffff, #f0f0f0);
}
#menu-button {
    position: absolute;
    top: 5px;
    left: 5px;
    z-index: 1003;
    background-color: #ffffff;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.25);
    font-size: 26px;
    color: #333;
    user-select: none;
    transition: background-color 0.2s ease;
    height: 50px;
  }
  
  #menu-button:hover {
    background-color: #f0f0f0;
  }
  
  #side-menu {
    position: absolute;
    height: 100vh;
    top: 0px;
    left: 0px;
    z-index: 1000;
    /* background: white; */
    background:linear-gradient(to bottom, #007bff, #00c6ff);
    padding: 12px 0;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    opacity: 0;
    pointer-events: none;
    width: 250px;
  }
  
  #side-menu.visible {
    opacity: 1;
    /* transform: scaleY(1); */
    pointer-events: auto;
  }
  
  #side-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
    margin-top: 10px;
  }
  
  #side-menu li {
    padding: 10px 16px;
    transition: background-color 0.2s;
  }
  
  #side-menu li:not(:last-child) {
    /* border-bottom: 1px solid #e0e0e0; */
  }
  
  /* #side-menu li:last-child {
    border-top: 1px solid #ccc;
    margin-top: 8px;
  } */
  
  #side-menu a {
    text-decoration: none;
    color: white;
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 20px;
  }
  
  #side-menu a:hover {
    background-color: rgba(255,255,255,0.2);
    /* color: black; */
    font-weight: bold;
    border-radius: 6px;
  }
   
  #side-menu a i {
    width: 22px;
    text-align: center;
    margin-right: 10px;
    font-size: 16px;
    color: white;
  }
  #side-menu a i:hover {
    color: black;
  }
  .spanmenu{
    margin-top: 10px;
  }
  .hidden {
    display: none;
  }
  #app-header {
    position: absolute;
    top: 0;
    left: 250px;
    width: calc(100vw - 250px);
    height: 60px;
    background: linear-gradient(to right, #007bff, #00c6ff);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 1002;
  }
  .logoimg{
    width: 112px;
  margin-left: 68px;
  }
  .logo-title {
    display: flex;
    align-items: center;
    color: white;
    font-size: 34px;
    font-weight: 600;
    font-family: 'Segoe UI', 'Roboto', sans-serif;
  }
  
  .logo-title i {
    margin-right: 10px;
    font-size: 24px;
  }
.arg {
    background-image: url("../img/argentina.png");
    font-size: 0px;
}

.eeuu {
    background-image: url("../img/eeuu.png");
    font-size: 0px;
}

.lang_button {
    position: absolute;
    right: 10px;
    width: 70px;
    height: 55px;
    outline: none;
    background-repeat: no-repeat;
    background-size: cover;
}



.icon-button {
  position: relative;
  background-color: #ffa500;
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 20px;
  width: 48px;
  height: 48px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.icon-button:hover {
  background-color: #e69500;
}

/* Tooltip */
.icon-button::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: #fff;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  opacity: 0;
  white-space: nowrap;
  pointer-events: none;
  transition: opacity 0.2s ease;
}

.icon-button:hover::after {
  opacity: 1;
}
