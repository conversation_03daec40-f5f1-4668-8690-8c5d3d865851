import fetch from 'node-fetch';
import colors from '../colors.js';
import inputs from '../inputs.js';

export default async function createUserSameUserName() {
  let startTimestamp;
  let finishTimestamp;
  console.log();
  console.log(colors.bg.black + colors.fg.green + "#############################################" + colors.reset);
  console.log(colors.bg.black + colors.fg.green + 'START TEST => CREATE USER WITH SAME USERNAME' + colors.reset);
  console.log(colors.bg.black + colors.fg.green + "#############################################" + colors.reset);
  console.log();

  const endpoint = "/users";
  const method = "POST";

  let Request = {
    name: inputs.name,
    surname: inputs.surname,
    username: inputs.username,
    password: inputs.password,
    mail: inputs.mail,
    tenantId: inputs.tenantId,
  };
  startTimestamp = Date.now();

  try {
    const response = await fetch('http://127.0.0.1:8080' + endpoint, {
      method: method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(Request)
    });
    if (process.env.DEBUGRESPONSES === 'true') {
      console.log('Backend_Response:');
      console.log(response.status + " : " + response.statusText + " - " + await response.text())
      console.log();
    }
    finishTimestamp = Date.now();
    const TotalTime = finishTimestamp - startTimestamp;
    console.log('Response time: ' + TotalTime + " ms");
    if (response.status != 400) return false;
    else return true;
  } catch (error) {
    console.error('Error:', error);
    return false;
  }
}



