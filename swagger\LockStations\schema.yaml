components:
  schemas:
    LockStation:
      type: object
      required:
        - serial
        - tenantId
        - phone
        - modelId
        - description
      properties:
        id:
          type: uuid
          description: id
          example: "ad763a26-00ec-4c43-9611-7b4b978990eb"
          readOnly: true
        serial:
          type: string
          description: Serial number of the station
          example: AF324GAC0E123
        tenantId: 
          type: integer
          description: Tenant Id
          example: 2
          readOnly: true
        stationStartIndex: 
          type: integer
          description: >
            Start index of the lock station in the station
            
            If the lock station model has 1 spot, it will be the station index

            If the lock station model has more than 1 spot,
            it will set the start station index and the rest will be assigned in ascending order
          example: 1
        phone: 
          type: integer
          description: Phone number to communicate with lock station
          example: 1155512345
        modelId:
          type: uuid
          description: Id of the lock stations model
          example: "ad763a26-00ec-4c43-9611-7b4b978990eb"
        description:
          type: string
          description: Description of the Station
          example: My Lock Station
