<div id="map-container" style="position: relative; height: 600px;">
  <div id="map" style="height: 100%;"></div>
  <div id="map-spinner" style="
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255,255,255,0.7);
        width: 100%;
        height: 100%;
      ">
    <div class="spinner-border" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
</div>


<script>
  function getMarkerClusterGroup({
    data,
    cssType,
    icon,
    popupContentFn
  }) {
    if (data.length === 0) return null;

    const cluster = L.markerClusterGroup({
      iconCreateFunction: function(cluster) {
        const childCount = cluster.getChildCount();
        let clusterSizeClass = ' marker-cluster-';
        if (childCount < 10) {
          clusterSizeClass += 'small';
        } else if (childCount < 100) {
          clusterSizeClass += 'medium';
        } else {
          clusterSizeClass += 'large';
        }
        return L.divIcon({
          html: `<div><span>${childCount}</span></div>`,
          className: `marker-cluster bg-${cssType} ${clusterSizeClass}`,
          iconSize: new L.Point(40, 40)
        });
      }
    });

    const dataIcon = L.divIcon({
      html: `<i class="fas ${icon} fa-3x text-${cssType}"></i>`,
      className: 'custom-div-icon',
      iconSize: [48, 48],
      iconAnchor: [24, 24],
      popupAnchor: [-12, -24]
    });

    data.forEach(item => {
      const marker = L.marker([item.lat, item.lon], {
          icon: dataIcon
        })
        .bindPopup(popupContentFn(item));
      cluster.addLayer(marker);
    });

    return cluster;
  }
</script>

<script>
  (function() {
    const layers = [{
        key: 'stations',
        switchId: 'stationsSwitch',
        cssType: 'station',
        icon: 'fa-charging-station',
        popupContentFn: station => `<strong>${station.name}</strong><br>${station.address}`
      },
      {
        key: 'devices',
        switchId: 'devicesSwitch',
        cssType: 'bike',
        icon: 'fa-bicycle',
        popupContentFn: device => `<strong>${device.serial}</strong><br>${device.type}`
      }
    ];

    document.addEventListener("DOMContentLoaded", function() {
      const map = L.map("map").setView([-34.589707, -58.439295], 13);

      L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
        attribution: "&copy; OpenStreetMap contributors"
      }).addTo(map);

      const clusters = {};

      window.App.subscribe(['stations', 'devices'], ({
        stations,
        devices
      }) => {
        const stateData = {
          stations,
          devices
        };

        const spinner = document.getElementById('map-spinner');
        if (stations === null || devices === null) {
          spinner.style.display = 'flex';
          return;
        }
        spinner.style.display = 'none';

        layers.forEach(layer => {
          const data = stateData[layer.key]?.filter(item => item.lat !== null && item.lon !== null) || [];

          // Remove old cluster if it exists
          if (clusters[layer.key]) map.removeLayer(clusters[layer.key]);

          // Create new cluster
          const cluster = getMarkerClusterGroup({
            data,
            cssType: layer.cssType,
            icon: layer.icon,
            popupContentFn: layer.popupContentFn
          });

          clusters[layer.key] = cluster;

          // Add cluster to map if switch is checked
          const switchEl = document.getElementById(layer.switchId);
          if (switchEl.checked && cluster) map.addLayer(cluster);

          // Listen to switch changes
          switchEl.onchange = () => {
            if (switchEl.checked && cluster) map.addLayer(cluster);
            else if (cluster) map.removeLayer(cluster);
          };
        });
      });
    });
  })();
</script>