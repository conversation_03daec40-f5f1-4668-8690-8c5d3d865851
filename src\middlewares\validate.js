import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt'
import validator from 'validator';

const saltrounds = 10;

async function hassPassword(plainpassword) {
    const hash = await bcrypt.hash(plainpassword, saltrounds);
    return hash;
}
async function comparePassword(plainpassword, hashedpassword) {
    const isValid = await bcrypt.compare(plainpassword, hashedpassword);
    return isValid;
}

const isUUID = (value) => {
    if (!validator.isUUID(value)) {
        return false;
    }
    return true;
}

const validateEmail = (email) => {
    return String(email)
        .toLowerCase()
        .match(
            /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        );
};
const validatePassword = (password) => {
    return String(password)
        .match(
            /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/
        );

}
const validate_new_user = async (req, res, next) => {
    try {
        if (!req.body.tenantId) {
            return res.status(400).json({ error: 'TenantId not provided.' });
        }
        if (!req.body.password) {
            return res.status(400).json({ error: 'Password not provided.' });
        }
        if (!validatePassword(req.body.password)) return res.status(400).json({ error: 'Password doesnt match "Minimum eight characters, at least one letter and one number"' });;
        req.body.unhashedpassword = req.body.password;
        req.body.password = await hassPassword(req.body.password);
        if (!req.body.mail) {
            return res.status(400).json({ error: 'Mail not provided.' });
        }
        else {
            const validated_mail = validateEmail(req.body.mail);
            if (!validated_mail) return res.status(400).json({ error: 'Mail format incorrect.' });
            req.body.mail = validated_mail[0];
        }
        if (!req.body.username) req.body.username = req.body.mail;
        next();
    } catch (error) {
        return res.status(500).send({ error: 'Internal server error' })
    }

};

export default { validate_new_user, validatePassword, hassPassword, comparePassword, isUUID };
