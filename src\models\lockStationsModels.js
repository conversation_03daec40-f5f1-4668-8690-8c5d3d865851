import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const LockStationsModel = sequelize.define('LockStationsModels', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    name: { type: DataTypes.STRING, allowNull: false },
    model: { type: DataTypes.STRING, allowNull: true },
    spots: { type: DataTypes.INTEGER, allowNull: false },
    protocol: { type: DataTypes.STRING, allowNull: false },
    description: { type: DataTypes.STRING, allowNull: true }

});

export default LockStationsModel;