import Device from '../../../models/device.js';
import TCPServer from '../../../servertcp.js';
import IoTMessageParser from './parser.js';
import IoTResponses from './responses.js';

const TCP_PORT = process.env.OMNI_PORT_DEBUG;
const TCP_HOST = '0.0.0.0';
const parser = new IoTMessageParser();
const Responses = new IoTResponses();
const server = new TCPServer(TCP_PORT, TCP_HOST);
const Timeout = 600000; //10 min
const connectedClients = new Map();

function socketTimeOut(socket) {
    if (socket.timer) clearTimeout(socket.timer);
    socket.timer = setTimeout(async function () {
        if (socket.imei) {
            const _device = await Device.findOne({
                where: { serial: socket.imei }
            });
            if (!_device) {
                console.log("Device " + _data.imei + " not found");
                return;
            }
            if (_device.connected != false) {
                _device.connected = false;
                if (connectedClients.has(_device.serial)) {
                    connectedClients.delete(_device.serial);
                }
                await _device.save();
            }
            console.log("Connection with " + socket.imei + " closed for timeout");
        }
        socket.end();
    }, Timeout);
    return;
}

function SendData(data, socket) {
    server.send(socket, data);
}

server.on('ready', () => {
    console.log('OMNI debug server ready!');
});

server.on('connection', async (socket) => {
    const remoteAddress = socket.remoteAddress + ":" + socket.remotePort;
    console.log(`Nueva conexión al server de debug desde: ${remoteAddress}`);
    try {
        socketTimeOut(socket);
    } catch (error) {
        return;
    }
});

server.on('data', async (socket, data) => {
    const remoteAddress = socket.remoteAddress + ":" + socket.remotePort;
    const dataString = data.toString().trim();
    const serial = '865209078369528';

    socketTimeOut(socket);
    try {
        console.log(data);
        console.log(dataString);
        if (dataString.includes('Q0')) {
            console.log('Configure position upload');
            const response = Responses.buildMessage(serial, 'D1', [20]);
            server.send(socket, response);
        }
        return;
    } catch (error) {
        return;
    }
});

server.on('disconnect', async (socket) => {
    const remoteAddress = socket.remoteAddress + ":" + socket.remotePort;
    console.log(`Conexión cerrada: ${remoteAddress}`);
});

server.on('error', (socket, err) => {
    const remoteAddress = socket.remoteAddress + ":" + socket.remotePort;
    console.error(`Error en la conexión ${remoteAddress}:`, err.message);
});

server.on('serverError', (err) => {
    console.error('Error general del servidor TCP:', err.message);
    if (err.code === 'EADDRINUSE') {
        console.error("La dirección " + HOST + ":" + PORT + "ya está en uso.");
    }
});

server.on('close', () => {
    console.log('El servidor TCP se ha apagado completamente.');
});

server.start()
    .then(() => {
        console.log('Servidor debug iniciado con éxito.');
    })
    .catch(err => {
        console.error('No se pudo iniciar el servidor TCP:', err);
    });

process.on('SIGINT', () => {
    console.log('Detectada señal SIGINT. Cerrando servidor...');
    server.stop()
        .then(() => process.exit(0))
        .catch(err => {
            console.error('Error al cerrar el servidor por SIGINT:', err);
            process.exit(1);
        });
});

process.on('SIGTERM', () => {
    console.log('Detectada señal SIGTERM. Cerrando servidor...');
    server.stop()
        .then(() => process.exit(0))
        .catch(err => {
            console.error('Error al cerrar el servidor por SIGTERM:', err);
            process.exit(1);
        });
});
// }


export default { SendData, connectedClients, server };