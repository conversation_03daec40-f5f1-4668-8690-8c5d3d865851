<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 165 165">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-1, .cls-2, .cls-3, .cls-4 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: #6a0cf8;
      }

      .cls-3 {
        fill: none;
      }

      .cls-4 {
        fill: #309;
      }
    </style>
    <linearGradient id="linear-gradient" x1=".13" y1="82.5" x2="153.24" y2="82.5" gradientUnits="userSpaceOnUse">
      <stop offset=".13" stop-color="#6a0cf8"/>
      <stop offset=".56" stop-color="#fd16b8"/>
      <stop offset=".97" stop-color="#fdba7f"/>
    </linearGradient>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-3" d="m140.58,9.06h0c-37.78-12.09-78.38-12.09-116.16,0h0c-7.3,2.34-13.02,8.05-15.35,15.35h0c-12.09,37.78-12.09,78.39,0,116.16h0c2.34,7.3,8.05,13.02,15.35,15.35h0c37.78,12.09,78.38,12.09,116.16,0h0c7.3-2.34,13.02-8.05,15.35-15.35h0c12.09-37.78,12.09-78.39,0-116.16h0c-2.34-7.3-8.05-13.02-15.35-15.35Z"/>
      <g>
        <path class="cls-2" d="m124.96,122.49c-10.68,0-20.73-4.16-28.28-11.72l-14.14-14.14-14.14,14.14c-7.56,7.55-17.6,11.72-28.28,11.72s-20.73-4.16-28.28-11.72c-15.6-15.6-15.6-40.97,0-56.57,15.6-15.6,40.97-15.6,56.57,0l14.14,14.14,14.14-14.14c15.6-15.59,40.97-15.6,56.57,0l-14.14,14.14c-7.8-7.8-20.49-7.8-28.28,0l-14.14,14.14,14.14,14.14c3.78,3.78,8.8,5.86,14.14,5.86s10.36-2.08,14.14-5.86l14.14,14.14c-7.56,7.55-17.6,11.72-28.28,11.72ZM40.11,62.5c-5.12,0-10.24,1.95-14.14,5.85-7.8,7.8-7.8,20.49,0,28.28,7.8,7.8,20.49,7.8,28.28,0l14.14-14.14-14.14-14.14c-3.9-3.9-9.02-5.85-14.14-5.85Z"/>
        <path class="cls-1" d="m11.82,54.21C4.02,62,.12,72.25.13,82.49h19.99c0-5.12,1.95-10.24,5.85-14.14,3.9-3.9,9.02-5.85,14.14-5.85s10.24,1.95,14.14,5.85l14.14,14.14,14.14,14.14,14.14,14.14c7.56,7.55,17.6,11.72,28.28,11.72s20.73-4.16,28.28-11.72l-14.14-14.14c-3.78,3.78-8.8,5.86-14.14,5.86s-10.36-2.08-14.14-5.86l-14.14-14.14h0s-14.14-14.14-14.14-14.14l-14.14-14.14c-15.6-15.59-40.97-15.6-56.57,0Z"/>
        <rect class="cls-4" x="72.53" y="72.49" width="20" height="20" transform="translate(-34.16 82.52) rotate(-45)"/>
      </g>
    </g>
  </g>
</svg>