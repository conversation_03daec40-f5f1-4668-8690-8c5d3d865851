services:
  app:
    hostname: bikesharing-server
    image: node:latest
    command: sh -c "apt update && apt install net-tools && npm i && npm run start"
    restart: unless-stopped
    env_file: ./.env
    ports:
      - 8080:8080
      - 8081:8081
      - 8095:8095
      - 8082:8082
      - 9229:9229
      - 8085:8085 #OMNI
      - 8086:8086 #OMNI debug
      - 8087:8087 #OMNI lock station
    expose:
      - 8082
    working_dir: /app
    volumes:
      - ./:/app
    stdin_open: true
    tty: true
    environment:
      - LETSENCRYPT_HOST=develop.com.ar
      - LETSENCRYPT_EMAIL=<EMAIL>
      - VIRTUAL_HOST=develop.com.ar
      - VIRTUAL_PORT=8080

  pgadmin4:
    image: elestio/pgadmin:latest
    restart: always
    env_file: ./.env
    environment:
      PGADMIN_DEFAULT_EMAIL: $PGADMIN_DEFAULT_EMAIL
      PGADMIN_DEFAULT_PASSWORD: $PGADMIN_DEFAULT_PSW
      PGADMIN_LISTEN_PORT: $PGADMIN_DOCKER_PORT
    ports:
    - $PGADMIN_LOCAL_PORT:$PGADMIN_DOCKER_PORT
    volumes:
      - pgadmin_data:/var/lib/pgadmin

  postgresdb:
    image: postgres:latest
    restart: unless-stopped
    env_file: ./.env
    environment:
      - POSTGRES_USER=$POSTGRESDB_USER
      - POSTGRES_PASSWORD=$POSTGRESDB_ROOT_PASSWORD
      - POSTGRES_DB=$POSTGRESDB_NAME
    ports:
      - $POSTGRESDB_LOCAL_PORT:$POSTGRESDB_DOCKER_PORT
    volumes:
      - ./db:/var/lib/postgresql/data

volumes:
  pgadmin_data: