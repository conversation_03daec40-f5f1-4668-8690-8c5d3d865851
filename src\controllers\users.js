import { OAuth2Client } from 'google-auth-library';
import { google } from 'googleapis';
import jsonwebtoken from 'jsonwebtoken';
import authenticateToken from '../functions/authenticate.js';
import validate from '../middlewares/validate.js';
import Configuration from '../models/configuration.js';
import { default as User, default as UserModel } from '../models/users.js';


//OAUTH2
const CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
const REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI;

if (!CLIENT_ID || !CLIENT_SECRET || !REDIRECT_URI) {
    console.error('Missing Google OAuth environment variables. Please check your .env file.');
    // return res.status(500).send({ error: "Internal server error" });
}
const oAuth2Client = new OAuth2Client(
    CLIENT_ID,
    CLIENT_SECRET,
    REDIRECT_URI
);

const scopes = [
    'https://www.googleapis.com/auth/userinfo.profile',
    'https://www.googleapis.com/auth/userinfo.email'
];


// 1. Initiate Google OAuth Login
const authGoogleInit = (req, res) => {
    const authorizeUrl = oAuth2Client.generateAuthUrl({
        access_type: 'offline', // Request a refresh token for long-lived access
        scope: scopes.join(' '), // Combine scopes with a space
        prompt: 'consent' // Forces consent screen every time (useful for testing)
    });
    res.redirect(authorizeUrl);
}

const authGoogleCallback = async (req, res) => {
    const { code } = req.query;

    if (!code) return res.status(400).send('Authorization code missing.');

    try {
        const { tokens } = await oAuth2Client.getToken(code);
        oAuth2Client.setCredentials(tokens);

        console.log('Successfully obtained tokens:', tokens);

        // At this point, `tokens` will contain:
        // - `access_token`: Use this to make API calls.
        // - `refresh_token`: (If `access_type: 'offline'` was requested and it's the first time) Store this securely in your database.
        // - `id_token`: (If requested, e.g., for user info) A JWT that contains user profile information.

        // --- IMPORTANT: Store refresh token securely ---
        if (tokens.refresh_token) {
            console.log('Refresh token obtained. Store this securely:', tokens.refresh_token);
        }

        // --- Example: Get user profile information using the access token ---
        const oauth2 = google.oauth2({ version: 'v2', auth: oAuth2Client });
        const userInfo = await oauth2.userinfo.get();
        console.log('User Profile:', userInfo.data);

        res.send(`
            <h1>Google OAuth Successful!</h1>
            <p>Welcome, ${userInfo.data.name} (${userInfo.data.email})!</p>
            <p>Access Token: ${tokens.access_token}</p>
            <p>Refresh Token: ${tokens.refresh_token ? 'Available (stored securely)' : 'Not obtained (first time or already obtained)'}</p>
            <p>You can now use the access token to call Google APIs.</p>
            <p><a href="/">Go Home</a></p>
        `);

    } catch (error) {
        console.error('Error during token exchange:', error.message);
        res.status(500).send('Authentication failed.');
    }
}

const getUsers = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (!id) return res.status(400).send({ error: "User id not provided" });

        if (user.type == "Customer") {
            delete user.password;
            if (id == user.id) return res.json(user);
            else return res.status(400).send({ error: "You dont have permission to read the user" });
        }
        else if (user.type == "Tenant") {
            const _user = await UserModel.findOne({
                where: {
                    tenantId: user.id,
                    id: (id * 1)
                }
            });
            if (!_user) return res.status(404).send({ error: "User not found" });
            delete _user.password;
            return res.status(200).send(_user);
        }
        else if (user.type == "Sysadmin") {
            const user = await UserModel.findOne({
                where: {
                    id: id
                }
            });
            delete user.password;
            return res.status(200).send(user);
        }
        return res.status(500).send({ error: "Internal Server Error" });

    } catch (error) {
        return res.status(500).send({ error: "Internal Server Error" });
    }
};

const getTotalUsers = async (req, res) => {
    try {
        const user = req.user;

        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to get the user count" });

        const users = await UserModel.findAll({
            where: {
                tenantId: user.id,
            },
        });
        return res.status(200).send({total: users.length});
    } catch (error) {
        return res.status(500).send({ error: "Internal Server Error" });
    }
};

const newUser = async (req, res) => {
    try {
        req.body.type = "Customer";
        const TenantId = await UserModel.findOne({ where: { id: req.body.tenantId, type: 'Tenant' } });
        if (!TenantId) return res.status(400).json({ error: "Invalid Tenant" });

        const body = req.body;

        const user = await UserModel.findOne({
            where: {
                mail: body.mail,
                deleted: true
            }
        });
        if (user) {
            const isValid = await validate.comparePassword(req.body.unhashedpassword, user.password);
            if (!isValid) {
                return res.status(400).send({ error: "Invalid password on an old account, contact support" });
            }
            user.deleted = false;
            await user.save();
            return res.status(200).json(user);
        }
        const newUser = await UserModel.create(req.body);
        return res.status(200).json(newUser);
    } catch (error) {
        return res.status(400).json({ error: error });
    }
};

const changePassword = async (req, res) => {
    try {
        const user = req.user;
        const isValid = await validate.comparePassword(req.body.password, user.password);
        if (!isValid) return res.status(400).send({ error: "Password incorrect" });
        if (!validate.validatePassword(req.body.newpassword)) return res.status(400).send({ error: "Password doenst match: Minimum eight characters, at least one letter and one number" });;
        const newpasswordHashed = await validate.hassPassword(req.body.newpassword);
        user.set({
            password: newpasswordHashed
        })
        await user.save();
        return res.status(200).send();
    } catch (error) {
        return res.status(500).send({ error: "Internal Server error" })
    }
}

const loginUser = async (req, res) => {
    try {
        const userName = req.body.username;
        if (!userName) return res.status(400).send({ error: "Username not provided" });
        if (!req.body.password) return res.status(400).send({ error: "Password not provided" });


        var user = await UserModel.findOne({ where: { username: userName } });
        if (!user) user = await UserModel.findOne({ where: { mail: userName } });

        if (!user) {
            return res.status(404).send({ error: "User not found" });
        }
        if (user.deleted) {
            return res.status(400).send({ error: "User deleted" });
        }
        const isValid = await validate.comparePassword(req.body.password, user.password);
        if (!isValid) {
            return res.status(400).send({ error: "Invalid password" });
        }
        var accessToken;
        var refreshToken;
        const isStillValidToken = authenticateToken(user.jwt);
        if (isStillValidToken) {
            accessToken = user.jwt;
            refreshToken = user.rjwt;

        }
        else {
            accessToken = jsonwebtoken.sign(
                { id: user.id, username: user.username },
                process.env.TOKENSECRET,
                { expiresIn: '1h' }
            );

            refreshToken = jsonwebtoken.sign(
                { id: user.id, username: user.username },
                process.env.REFRESHSECRET,
                { expiresIn: '6h' }
            );
            user.set({
                jwt: accessToken,
                rjwt: refreshToken
            });
            await user.save();
        }

        return res.status(200).send({
            accessToken,
            refreshToken
        });
    } catch (error) {
        return res.status(500).json({ error: "Internal server error." + JSON.stringify(err) });
    }

};

const refreshToken = async (req, res) => {
    try {
        const refreshToken = req.body.refreshToken;

        if (!refreshToken) {
            return res.status(401).json({ error: "Refresh token is required." });
        }

        jsonwebtoken.verify(refreshToken, process.env.REFRESHSECRET, (err, user) => {
            if (err) {
                return res.status(401).json({ error: "Invalid or expired refresh token." });
            }
            const accessToken = jsonwebtoken.sign(
                { id: user.id, username: user.username },
                process.env.TOKENSECRET,
                { expiresIn: '1h' }
            );
            return res.json({ accessToken });
        });
    } catch (error) {
        return res.status(500).json({ error: "Internal server error." + JSON.stringify(err) });
    }
}

const deleteUser = async (req, res) => {
    try {
        const user = req.user;
        if (user.deleted == true) return res.status(400).send("User already deleted");
        const isValid = await validate.comparePassword(req.body.password, user.password);
        if (!isValid) return res.status(400).send({ error: "Password incorrect" });
        user.set({
            deleted: true
        })
        const response = await user.save();
        return res.status(200).send({ response: "User deleted" })
    }
    catch (err) {
        return res.status(500).json({ error: "Internal server error." + JSON.stringify(err) });
    }
}

const configureTenant = async (req, res) => {
    try {
        const user = req.user;
        const { paymentMethod } = req.body;
        const { price } = req.body;

        if (user.deleted == true) return res.status(400).send("User already deleted");
        if (!paymentMethod || !price) return res.status(400).send({ error: "Parameters not provided" });
        if (user.type != "Tenant") {
            return res.status(400).send({ error: "User must be tenant" });
        }

        let configuration = await Configuration.findOne({
            where: { tenantId: user.id }
        })
        if (!configuration) {
            configuration = new Configuration();
            configuration.price = price;
            configuration.currency = paymentMethod;
            configuration.tenantId = user.id;
            await configuration.save();
            return res.status(200).send({ response: "Configuration updated" })
        }
        if (paymentMethod === configuration.currency) {
            configuration.price = price;
            await configuration.save();
            return res.status(200).send({ result: "Price has been changed" })
        }
        let updateUsers = await User.findAll({
            where: { tenantId: user.id }
        })

        // si pasa de horas a km
        // 1 hora (60min)-> 25KM
        // 1 KM -> 3 min
        // 10KM -> 1 Viaje
        // 1 Viaje -> 10KM
        for (let updateUser of updateUsers) {
            switch (configuration.currency) {
                case 'TIME':
                    if (paymentMethod == 'KM') updateUser.credit = updateUser.credit * 0.5;
                    if (paymentMethod == 'FIXED') updateUser.credit = updateUser.credit / 30; //viaje promedio 30 minutos
                    await updateUser.save();
                    break;
                case 'KM':
                    if (paymentMethod == 'TIME') updateUser.credit = updateUser.credit * 3;
                    if (paymentMethod == 'FIXED') updateUser.credit = updateUser.credit / 20; //viaje promedio 20 KM
                    await updateUser.save();
                    break;
                case 'FIXED':
                    if (paymentMethod == 'TIME') updateUser.credit = updateUser.credit * 30; // viaje a 30 minutos
                    if (paymentMethod == 'KM') updateUser.credit = updateUser.credit * 20; //viaje promedio 20 KM
                    await updateUser.save();
                    break;
                default:
                    break;
            }

        }
        configuration.price = price;
        configuration.currency = paymentMethod;
        await configuration.save();
        return res.status(200).send({ result: "Users updated" });
    }
    catch (err) {
        return res.status(500).json({ error: "Internal server error." + JSON.stringify(err) });
    }
}

const getTenantConfiguration = async (req, res) => {
    try {
        const user = req.user;
        
        if (user.deleted == true) return res.status(400).send("Invalid user");
        if (user.type == "Tenant") {
            let configuration = await Configuration.findOne({
                where: { tenantId: user.id }
            })
            if (!configuration) return res.status(404).send({ error: "Configuration not found" })
            return res.status(200).send(configuration);
        }
        if (user.type == "Customer") {
            let configuration = await Configuration.findOne({
                where: { tenantId: user.tenantId }
            })
            if (!configuration) return res.status(404).send({ error: "Configuration not found" })
            return res.status(200).send(configuration);
        }
        return res.status(400).send({ error: "Invalid user type" })
    }
    catch (err) {
        return res.status(500).json({ error: "Internal server error." + JSON.stringify(err) });
    }
}

const addCurrencyToWallet = async (req, res) => {
    try {
        const paymentTokens = [process.env.PAYWAY_WALLET_AUTH_TOKEN,process.env.MERCADOPAGO_WALLET_AUTH_TOKEN];
        const user = req.user;
        const { amount } = req.body;
        const { token } = req.body;

        if (!token) return res.status(400).send({ error: "Token not provided" });
        if (!paymentTokens.includes(token)) return res.status(400).send({ error: "Invalid token"});
        if (!amount) return res.status(400).send({ error: "Amount not provided" });
        if (user.deleted == true) return res.status(400).send("Invalid user");
        if (user.type !== "Customer") {
            return res.status(400).send({ error: "Only customers have wallets" });
        }

        user.credit += amount;
        await user.save();
        return res.status(200).send({'credit': user.credit});
    }
    catch (err) {
        return res.status(500).json({ error: "Internal server error." + JSON.stringify(err) });
    }
}

const getWallet = async (req, res) => {
    try {
        const user = req.user;
        if (user.deleted == true) return res.status(400).send("Invalid user");
        if (user.type !== "Customer") {
            return res.status(400).send({ error: "Only customers have wallets" });
        }

        return res.status(200).send({'credit': user.credit});
    }
    catch (err) {
        return res.status(500).json({ error: "Internal server error." + JSON.stringify(err) });
    }
}

export default {
    getUsers, getTotalUsers, newUser, loginUser, refreshToken, changePassword, deleteUser,
    authGoogleInit, authGoogleCallback, configureTenant, getTenantConfiguration,
    getWallet , addCurrencyToWallet
};