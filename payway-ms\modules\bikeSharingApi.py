import requests

def assign_device_to_customer(api_url, customer_jwt, serial, duration_minutes):
    """
    Assign a device to a customer by making a POST request to the /assign endpoint.

    :param api_url: Base URL of the bikesharing-ms API (e.g., http://localhost:9191)
    :param customer_jwt: JWT token for the customer (string)
    :param device_id: ID of the device to assign (string)
    :param duration_minutes: Duration in minutes for the assignment (int)
    :return: Response object from requests
    """
    url = f"{api_url}/devices/rent"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"{customer_jwt}"
    }
    payload = {
        "serial": serial,
        "duration_minutes": duration_minutes
    }
    response = requests.post(url, headers=headers, json=payload)
    return response



def add_currency_to_wallet(api_url, user_jwt, amount, token):
    """
    Add currency to a user's wallet by making a POST request to the /wallet endpoint.

    :param api_url: Base URL of the payway-ms API (e.g., http://localhost:9192)
    :param user_jwt: JWT token for the user (string)
    :param amount: Amount of currency to add (float)
    :param token: Payment service token (string)
    :return: Response object from requests
    """
    url = f"{api_url}/users/wallet"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"{user_jwt}"
    }
    payload = {
        "amount": amount,
        "token": token
    }
    response = requests.post(url, headers=headers, json=payload)
    return response