from flask import request
import hashlib
import hmac
import urllib.parse


class Webhook:
    def __init__(self, token: str):
        self.token = token

    def validate_webhook(self, incomming_request) -> bool:  # Changed `-> True` to `-> bool`
        try:
            # Obtain the x-signature value from the header
            xSignature = incomming_request.headers.get("x-signature")
            xRequestId = incomming_request.headers.get("x-request-id")

            if not xSignature or not xRequestId:
                raise ValueError("Missing required headers: x-signature or x-request-id")

            # Extract query parameters from the incoming request
            queryParams = incomming_request.args  # Use `.args` to access query parameters

            # Extract the "data.id" from the query params
            dataID = queryParams.get("data.id", "")

            # Separate the x-signature into parts
            parts = xSignature.split(",")

            # Initialize variables to store `ts` and `hash`
            ts = None
            hash = None

            # Iterate over the values to obtain `ts` and `v1`
            for part in parts:
                # Split each part into key and value
                keyValue = part.split("=", 1)
                if len(keyValue) == 2:
                    key = keyValue[0].strip()
                    value = keyValue[1].strip()
                    if key == "ts":
                        ts = value
                    elif key == "v1":
                        hash = value

            if not ts or not hash:
                raise ValueError("Missing required components in x-signature")

            # Obtain the secret key for the user/application
            secret = self.token

            # Generate the manifest string
            manifest = f"id:{dataID};request-id:{xRequestId};ts:{ts};"

            # Create an HMAC signature
            hmac_obj = hmac.new(
                secret.encode(), msg=manifest.encode(), digestmod=hashlib.sha256
            )

            # Obtain the hash result as a hexadecimal string
            sha = hmac_obj.hexdigest()

            # Compare the generated hash with the received hash
            return sha == hash

        except Exception as e:
            print(f"Validation error: {e}")
            return False
