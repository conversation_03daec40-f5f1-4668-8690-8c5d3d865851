import socket
import threading
import argparse

IMEI= "123456789123456"

INITIAL_DELIMITER = bytes([0xFF, 0xFF])

Q0_MESSAGE = f"*SCOR,OM,{IMEI},Q0,412,80,28#\n"
H0_MESSAGE = f"*SCOR,OM,{IMEI},H0,0,412,28,80,0#\n"
R0_UNLOCK_MESSAGE = f"*SCOS,OM,{IMEI},R0,0,20,1234,1497689816#\n"
R0_LOCK_MESSAGE= f"*SCOS,OM,{IMEI},R0,1,20,1234,1497689816#\n"

## PUT ALL MESSAGES IN A LIST
MESSAGES = [
    Q0_MESSAGE,
    H0_MESSAGE,
    R0_UNLOCK_MESSAGE,
    R0_LOCK_MESSAGE
]

def receive_messages(sock):
    """Thread function to continuously receive messages from server"""
    while True:
        try:
            data = sock.recv(1024)
            if not data:
                break
            print("\nReceived from server:", data.decode('utf-8'))
            print("Enter message to send (or 'quit' to exit): ", end='', flush=True)
        except ConnectionResetError:
            print("\nConnection lost with server")
            break
        except Exception as e:
            print(f"\nError receiving message: {e}")
            break

def start_client(host='127.0.0.1', port=65432):
    """Start the TCP client"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
        try:
            # Connect to server
            sock.connect((host, port))
            print(f"Connected to server at {host}:{port}")
            
            # Start receive thread
            receive_thread = threading.Thread(
                target=receive_messages, 
                args=(sock,), 
                daemon=True
            )
            receive_thread.start()
            
            # Main loop for sending messages
            while True:
                ### Show a list of available messages
                print("\nAvailable messages:")
                for i, message in enumerate(MESSAGES):
                    print(f"{i + 1}: {message.strip()}")
                ### allow the user to select a message by number
                print("0: Custom message")
                choice = input("Select a message to send (or 'quit' to exit): ")
                if choice.lower() == 'quit':
                    break
                if choice.isdigit() and 0 <= int(choice) <= len(MESSAGES):
                    if int(choice) == 0:
                        message = input("Enter your custom message: ")
                    else:
                        message = MESSAGES[int(choice) - 1]
                else:
                    print("Invalid choice. Please try again.")
                    continue
                
                
                print(f"Sending message: {message.strip()}")
                
                
                try:
                    sock.sendall( INITIAL_DELIMITER + message.encode('utf-8'))
                except Exception as e:
                    print(f"Error sending message: {e}")
                    break
            
            print("Disconnecting from server...")
        
        except ConnectionRefusedError:
            print(f"Could not connect to server at {host}:{port}")
        except Exception as e:
            print(f"An error occurred: {e}")

if __name__ == "__main__":

    
    parser = argparse.ArgumentParser(description='TCP Client')
    parser.add_argument('--host', default='127.0.0.1', help='Server IP address')
    parser.add_argument('--port', type=int, default=65432, help='Server port number')
    
    args = parser.parse_args()
    
    start_client(args.host, args.port)