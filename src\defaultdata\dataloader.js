import { Sequelize } from 'sequelize';
import sequelize from '../config/database.js';
import validate from '../middlewares/validate.js';
import User from '../models/users.js';

import dotenv from 'dotenv';
import Configuration from '../models/configuration.js';

dotenv.config();
const loadData = async () => {

  console.log('🔄 Syncing database...');
  await sequelize.sync({ force: false });

  console.log('✅ Database sync complete. Seeding data...');

  await _createSysadmin({
    name: process.env.SYSADMINNAME,
    surname: process.env.SYSADMINSURNAME,
    username: process.env.SYSADMINUSERNAME,
    password: process.env.SYSADMINPASSWORD,
    mail: process.env.SYSADMINMAIL,
  });
  await _createTenant({
    name: process.env.TENANTNAME,
    surname: process.env.TENANTSURNAME,
    username: process.env.TENANTUSERNAME,
    password: process.env.TENANTPASSWORD,
    mail: process.env.TENANTMAIL,
    tenantId: 2,
  });
  await _createTenantConfiguration({
    currency: 'WALLETTIME',
    price: 20,
    tenantId: 2,
  });
  await _createTenant({
    name: process.env.TENANT2NAME,
    surname: process.env.TENANT2SURNAME,
    username: process.env.TENANT2USERNAME,
    password: process.env.TENANT2PASSWORD,
    mail: process.env.TENANT2MAIL,
    tenantId: 3,
  });
  await _createTenantConfiguration({
    currency: 'WALLETTIME',
    price: 20,
    tenantId: 3,
  });
  await _createCustomer({
    name: process.env.CUSTOMERNAME,
    surname: process.env.CUSTOMERSURNAME,
    username: process.env.CUSTOMERUSERNAME,
    password: process.env.CUSTOMERPASSWORD,
    mail: process.env.CUSTOMERMAIL,
    tenantId: 2,
  });
};

async function _createSysadmin({name, surname, username, password, mail}) {
  try {
    const passwordHashed = await validate.hassPassword(password);
    await User.create({
      name: name,
      type: "Sysadmin",
      surname: surname,
      username: username,
      password: passwordHashed,
      mail: mail,
      validated: true,
    });
    console.log('✅ Sysadmin created: ' + name);
  } catch (error) {
    if (!error instanceof Sequelize.UniqueConstraintError) {
      console.error('❌ Error creating Sysadmin: ' + name);
    }
  }
}

async function _createTenant({name, surname, username, password, mail, tenantId}) {
  try {
    const passwordHashed = await validate.hassPassword(password);
    await User.create({
      name: name,
      type: "Tenant",
      surname: surname,
      username: username,
      password: passwordHashed,
      mail: mail,
      validated: true,
      tenantId: tenantId,
    });
    console.log('✅ Tenant created: ' + name);
  } catch (error) {
    if (!error instanceof Sequelize.UniqueConstraintError) {
      console.error('❌ Error creating Tenant: ' + name);
    }
  }
}

async function _createCustomer({name, surname, username, password, mail, tenantId}) {
  try {
    const passwordHashed = await validate.hassPassword(password);
    await User.create({
      name: name,
      type: "Customer",
      surname: surname,
      username: username,
      password: passwordHashed,
      mail: mail,
      validated: true,
      tenantId: tenantId,
    });
    console.log('✅ Customer created: ' + name);
  } catch (error) {
    if (!error instanceof Sequelize.UniqueConstraintError) {
      console.error('❌ Error creating Customer: ' + name);
    }
  }
}

async function _createTenantConfiguration({currency, price, tenantId}) {
  try {
    await Configuration.create({
      currency: currency,
      price: price,
      tenantId: tenantId,
    });
    console.log('✅ Tenant configuration created from id: ' + tenantId);
  } catch (error) {
    if (!error instanceof Sequelize.UniqueConstraintError) {
      console.error('❌ Error creating Tenant configuration from id: ' + tenantId);
    }
  }
}

export default loadData;
