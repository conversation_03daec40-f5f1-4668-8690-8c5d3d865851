import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    unique: true
  },
  type: {
    type: DataTypes.ENUM,
    values: ['Sysadmin', 'Tenant', 'Customer', 'technician'],
    allowNull: false
  },
  name: { type: DataTypes.STRING, allowNull: true },
  surname: { type: DataTypes.STRING, allowNull: true },
  username: { type: DataTypes.STRING, allowNull: false, unique: true },
  password: { type: DataTypes.STRING, allowNull: false },
  mail: { type: DataTypes.STRING, allowNull: false, unique: true },
  validated: { type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
  dni: { type: DataTypes.INTEGER, allowNull: true },
  phone: { type: DataTypes.INTEGER, allowNull: true },
  jwt: { type: DataTypes.STRING, allowNull: true },
  rjwt: { type: DataTypes.STRING, allowNull: true },
  deleted: { type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
  credit: { type: DataTypes.DOUBLE, allowNull: false, defaultValue: 0 },
});

export default User;