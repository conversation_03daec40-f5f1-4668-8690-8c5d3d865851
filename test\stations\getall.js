import fetch from 'node-fetch';
import inputs from '../inputs.js';
import colors from '../colors.js';

export default async function getAllStations(jwt, istenant) {
  let startTimestamp;
  let finishTimestamp;
  console.log();
  console.log(colors.bg.black + colors.fg.green + "#############################################" + colors.reset);
  console.log(colors.bg.black + colors.fg.green + 'START TEST => GET ALL STATION' + colors.reset);
  console.log(colors.bg.black + colors.fg.green + "#############################################" + colors.reset);
  console.log();

  const endpoint = "/stations/all";
  const method = "GET";

  startTimestamp = Date.now();
  try {
    const response = await fetch('http://127.0.0.1:8080' + endpoint, {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwt}`
      },
    });
    let resBody = await response.text();
    if (process.env.DEBUGRESPONSES === 'true') {
      console.log('Backend_Response:');
      console.log(response.status + " : " + response.statusText + " - " + resBody)
      console.log();
    }
    resBody = JSON.parse(resBody);
    finishTimestamp = Date.now();
    const TotalTime = finishTimestamp - startTimestamp;
    console.log('Response time: ' + TotalTime + " ms");
    if (response.status != 200) return false;
    else if (resBody.length > 0) return true;
    else return false;
  } catch (error) {
    console.error('Error:', error);
    return false;
  }
}



