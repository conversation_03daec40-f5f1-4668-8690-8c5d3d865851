import json
import uuid
from typing import Any, Dict, List, Optional

import requests
from pydantic import BaseModel, Field


class CardHolderIdentification(BaseModel):
    number: str = Field(..., example="12345678")
    type: str = Field(..., example="dni")


class CardData(BaseModel):
    card_number: str = Field(..., example="****************",
                             min_length=15, max_length=16)
    card_holder_name: str = Field(..., example="Jose Perez")
    card_expiration_month: str = Field(...,
                                       example="12", min_length=2, max_length=2)
    # Assuming YY format
    card_expiration_year: str = Field(...,
                                      example="30", min_length=2, max_length=2)
    security_code: str = Field(..., example="123", min_length=3, max_length=4)
    card_holder_identification: CardHolderIdentification

class TransactionRequieredElements(BaseModel):
    token: str = Field(..., example="token_1234567890abcdef")
    amount: float = Field(..., example=1000)


class Customer(BaseModel):
    email: str = Field(..., example="<EMAIL>")


class BillTo(BaseModel):
    city: str = Field(..., example="Buenos Aires")
    country: str = Field(..., example="AR")
    customer_id: str = Field(..., example="12345")
    email: str = Field(..., example="<EMAIL>")
    first_name: str = Field(..., example="John")
    last_name: str = Field(..., example="Doe")
    phone_number: str = Field(..., example="5491155551234")
    postal_code: str = Field(..., example="C1000AA")
    state: str = Field(..., example="C")  # Assuming C for CABA
    street1: str = Field(..., example="Av. Corrientes 123")
    street2: Optional[str] = Field("", example="")


class PurchaseTotals(BaseModel):
    currency: str = Field(..., example="ARS")
    amount: int = Field(..., example=122550)  # Changed to integer


class FraudDetection(BaseModel):
    send_to_cs: bool = Field(False, example=False)
    bill_to: BillTo
    purchase_totals: PurchaseTotals
    device_unique_id: Optional[str] = Field(None, example="2345")


class PaymentDetails(BaseModel):
    site_transaction_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    payment_method_id: int = Field(..., example=105)  # Example: Visa Crédito
    bin: str = Field(..., example="529991")
    amount: int = Field(..., example=122550)  # Changed to integer
    currency: str = Field(..., example="ARS")
    installments: int = Field(..., example=1)
    description: str = Field(..., example="Online purchase of electronics")
    customer: Customer
    # "single" or "recurring"
    payment_type: str = Field("single", example="single")
    sub_payments: List[Any] = Field([], example=[])
    fraud_detection: Optional[FraudDetection] = None
    dispatch_method: Optional[str] = None
    authorized_dni: Optional[str] = None


class PaymentStatusDetails(BaseModel):
    ticket_number: Optional[str] = None
    card_authorization_code: Optional[str] = None
    address_validation_code: Optional[str] = None
    error: Optional[Dict[str, Any]] = None
    reason: Optional[str] = None


class PaymentResponse(BaseModel):
    # Transaction ID from Payway, changed to int to match API response
    id: Optional[int] = None
    # e.g. approved, rejected, pending
    status: str = Field(..., example="approved")
    # Make optional as structure can vary
    status_details: Optional[PaymentStatusDetails] = None
    site_transaction_id: str
    payment_method_id: Optional[int] = None
    card_brand: Optional[str] = None  # e.g. VISA, MASTERCARD
    amount: int  # Changed to integer
    currency: Optional[str] = None
    installments: Optional[int] = None
    first_installment_expiration_date: Optional[str] = None
    fraud_detection: Optional[Dict[str, Any]] = None
    # Making fields optional to gracefully handle variations in the real API response

# --- Real Payway Service ---


class PaywayError(Exception):
    """Custom exception for Payway API errors."""
    pass


class Payway:
    """
    A class to handle sequential Payway API operations:
    1. Creating a payment token.
    2. Processing a payment using the generated token.
    """

    def __init__(self, token_api_key: str, payment_api_key: str, base_url: str):
        """
        Initializes the Payway client with API keys.
        """
        if not token_api_key or not payment_api_key:
            raise ValueError(
                "Both token_api_key and payment_api_key must be provided.")

        self.token_api_key = token_api_key
        self.payment_api_key = payment_api_key
        self.base_url = base_url

    def _get_card_bin(self, card_number: str) -> str:
        """Extracts the BIN (first 6 digits) from the card number."""
        if not card_number or len(card_number) < 6:
            raise ValueError("Invalid card number for BIN extraction.")
        return card_number[:6]

    def _create_token(self, card_data: dict) -> str:
        """
        Creates a payment token using the Payway API's /tokens endpoint.
        """
        url = f"{self.base_url}/tokens"
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'apikey': self.token_api_key
        }

        payload = card_data.copy()

        if 'fraud_detection' not in payload:
            payload['fraud_detection'] = {
                'device_unique_identifier': str(uuid.uuid4())}
        elif 'device_unique_identifier' not in payload['fraud_detection']:
            payload['fraud_detection']['device_unique_identifier'] = str(
                uuid.uuid4())

        try:
            response = requests.post(
                url, headers=headers, data=json.dumps(payload))
            response.raise_for_status()
            token_data = response.json()
            if 'id' not in token_data:
                raise PaywayError(
                    f"Token response missing 'id' key: {token_data}")
            return token_data['id']
        except requests.exceptions.HTTPError as e:
            error_text = e.response.text
            print(
                f"HTTP error creating token: {e.response.status_code} - {error_text}")
            raise PaywayError(f"Failed to create token: {error_text}") from e
        except requests.exceptions.RequestException as e:
            print(f"Network error creating token: {e}")
            raise PaywayError(
                f"Network error during token creation: {e}") from e
        except Exception as e:
            print(f"An unexpected error occurred during token creation: {e}")
            raise PaywayError(
                f"An unexpected error occurred during token creation: {e}") from e

    def build_payment_form(self, payment_details: dict, card_data: dict) -> dict:
        url = f"{self.base_url}/payments/link"
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'apikey': self.payment_api_key
        }
        token_id = self._create_token(card_data)
        payload = payment_details.copy()
        payload['token'] = token_id
        payload = payment_details.copy()

        try:
            response = requests.post(
                url, headers=headers, data=json.dumps(payload))
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            error_text = e.response.text
            print(
                f"HTTP error processing payment: {e.response.status_code} - {error_text}")
            raise PaywayError(
                f"Failed to process payment: {error_text}") from e
        except requests.exceptions.RequestException as e:
            print(f"Network error processing payment: {e}")
            raise PaywayError(
                f"Network error during payment processing: {e}") from e
        except Exception as e:
            print(
                f"An unexpected error occurred during payment processing: {e}")
            raise PaywayError(f"An unexpected error occurred: {e}") from e

    def process_payment(self, card_data: dict, payment_details: dict) -> dict:
        """
        Processes a payment by first creating a token and then using it to make a payment.
        """
        print("Step 1: Creating payment token...")
        token_id = self._create_token(card_data)
        print(f"Token created successfully: {token_id}")

        print("Step 2: Processing payment...")
        url = f"{self.base_url}/payments"
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'apikey': self.payment_api_key
        }

        payload = payment_details.copy()
        payload['token'] = token_id

        try:
            response = requests.post(
                url, headers=headers, data=json.dumps(payload))
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            error_text = e.response.text
            print(
                f"HTTP error processing payment: {e.response.status_code} - {error_text}")
            raise PaywayError(
                f"Failed to process payment: {error_text}") from e
        except requests.exceptions.RequestException as e:
            print(f"Network error processing payment: {e}")
            raise PaywayError(
                f"Network error during payment processing: {e}") from e
        except Exception as e:
            print(
                f"An unexpected error occurred during payment processing: {e}")
            raise PaywayError(f"An unexpected error occurred: {e}") from e


    def process_mobile_payment(self, token_id: str, payment_details: dict) -> dict:
            """
            Processes a payment by using a token to make a payment.
            """
            url = f"{self.base_url}/payments"
            headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'apikey': self.payment_api_key
            }

            payload = payment_details.copy()
            payload['token'] = token_id
            
            print(f"Payment Details: {json.dumps(payload, indent=2)}")

            try:
                response = requests.post(
                    url, headers=headers, data=json.dumps(payload))
                response.raise_for_status()
                
                
                
                return response.json()
            except requests.exceptions.HTTPError as e:
                error_text = e.response.text
                print(
                    f"HTTP error processing payment: {e.response.status_code} - {error_text}")
                raise PaywayError(
                    f"Failed to process payment: {error_text}") from e
            except requests.exceptions.RequestException as e:
                print(f"Network error processing payment: {e}")
                raise PaywayError(
                    f"Network error during payment processing: {e}") from e
            except Exception as e:
                print(
                    f"An unexpected error occurred during payment processing: {e}")
            raise PaywayError(f"An unexpected error occurred: {e}") from e

    def perform_refund(self, payment_id: dict) -> str:
        url = f"{self.base_url}/payments/{payment_id}/refunds"
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'apikey': self.payment_api_key
        }

        payload = {}

        try:
            response = requests.post(
                url, headers=headers, data=json.dumps(payload))
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            error_text = e.response.text
            print(
                f"HTTP error processing payment: {e.response.status_code} - {error_text}")
            raise PaywayError(
                f"Failed to process payment: {error_text}") from e
        except requests.exceptions.RequestException as e:
            print(f"Network error processing payment: {e}")
            raise PaywayError(
                f"Network error during payment processing: {e}") from e
        except Exception as e:
            print(
                f"An unexpected error occurred during payment processing: {e}")
            raise PaywayError(f"An unexpected error occurred: {e}") from e
