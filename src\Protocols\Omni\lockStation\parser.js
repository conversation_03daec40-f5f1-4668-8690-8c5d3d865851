export class LockStationChargingStatus {
    static ReceiverChargingStatus = {
            IDLE: 0,
            TRICKLE: 1,
            CONSTANT_CURRENT: 2,
            CONSTANT_VOLTAGE: 3,
            CHARGING_COMPLETED: 4,
            UNKNOWN: -1,

            fromValue(value) {
                return Object.keys(this).find(
                key => this[key] === value && typeof this[key] !== 'function'
                ) || 'UNKNOWN';
            }
        }

    constructor(deviceImei, locked, receiverBluetoothOk, senderReceiverConnectionOk,
        transmitterBluetoothOk, receiverChargingStatus, receiverTemperature,
        receiverEndVoltage, receiverEndCoilVoltage, receiverEndCurrent, isOutputMosOn,
        transmitterTemperature, transmitterVoltage, transmitterCurrent,
        metalForeignMatterDetected, overCurrentProtection, overVoltageProtection,
        overTemperatureProtection, transmitterFrequency, transmitterDutyCycle) {
            this.deviceImei = deviceImei;
            this.locked = locked;
            this.receiverBluetoothOk = receiverBluetoothOk;
            this.senderReceiverConnectionOk = senderReceiverConnectionOk;
            this.transmitterBluetoothOk = transmitterBluetoothOk;
            this.receiverChargingStatus = receiverChargingStatus;
            this.receiverTemperature = receiverTemperature;
            this.receiverEndVoltage = receiverEndVoltage;
            this.receiverEndCoilVoltage = receiverEndCoilVoltage;
            this.receiverEndCurrent = receiverEndCurrent;
            this.isOutputMosOn = isOutputMosOn;
            this.transmitterTemperature = transmitterTemperature;
            this.transmitterVoltage = transmitterVoltage;
            this.transmitterCurrent = transmitterCurrent;
            this.metalForeignMatterDetected = metalForeignMatterDetected;
            this.overCurrentProtection = overCurrentProtection;
            this.overVoltageProtection = overVoltageProtection;
            this.overTemperatureProtection = overTemperatureProtection;
            this.transmitterFrequency = transmitterFrequency;
            this.transmitterDutyCycle = transmitterDutyCycle;
        }
    
    static fromArray(fields) {
        let receiverBluetoothStatus = parseInt(fields[2]);
        let transmitterStatus = parseInt(fields[12]);
        return new LockStationChargingStatus(
            fields[0], // deviceImei
            0 === parseInt(fields[1]), // locked
            (receiverBluetoothStatus & 0x01) === 1, // receiverBluetoothOk
            ((receiverBluetoothStatus >> 1) & 0x01) === 1, // senderReceiverConnectionOk
            ((receiverBluetoothStatus >> 2) & 0x01) === 1, // transmitterBluetoothOk
            this.ReceiverChargingStatus.fromValue(parseInt(fields[3])), // receiverChargingStatus
            parseFloat(fields[4]), // receiverTemperature
            parseFloat(fields[5]), // receiverEndVoltage
            parseFloat(fields[6]), // receiverEndCoilVoltage
            parseFloat(fields[7]), // receiverEndCurrent
            1 === parseInt(fields[8]), // isOutputMosOn
            parseFloat(fields[9]), // transmitterTemperature
            parseFloat(fields[10]), // transmitterVoltage
            parseFloat(fields[11]), // transmitterCurrent
            (transmitterStatus & 0x01) === 1, // metalForeignMatterDetected
            ((transmitterStatus >> 1) & 0x01) === 1, // overCurrentProtection
            ((transmitterStatus >> 2) & 0x01) === 1, // overVoltageProtection
            ((transmitterStatus >> 3) & 0x01) === 1, // overTemperatureProtection
            parseFloat(fields[13]), // transmitterFrequency
            parseFloat(fields[14]) // transmitterDutyCycle
        );
    }
}

export class IoTMessageParser {
    constructor() {
        this.START_DELIMITER = '*';
        this.END_DELIMITER = '#';
        this.FIELD_SEPARATOR = ',';
        this.COMMAND_HEADER_SERVER_TO_IOT = '*SCOS';
        this.COMMAND_HEADER_IOT_TO_SERVER = '*SCOR';
    }

    parse(message) {
        const startIndex = message.indexOf(this.START_DELIMITER);
        if (startIndex === -1) {
            console.error("Parsing error: Could not find start delimiter " + this.START_DELIMITER);
            return null;
        }
        const endIndex = message.indexOf(this.END_DELIMITER, startIndex + 1);
        if (endIndex === -1) {
            console.error("Parsing error: Could not find end delimiter " + this.END_DELIMITER);
            return null;
        }

        // Include start delimiter, exclude end delimiter
        const rawPayload = message.substring(startIndex, endIndex);
        
        const parts = rawPayload.split(this.FIELD_SEPARATOR);

        if (parts.length < 4) {
            console.error("Error de parsing: Formato de comando inválido. Faltan partes.");
            return null;
        }

        const commandHeader = parts[0];
        const IMEI = parts[2];
        const CommandType = parts[3];
        const payloadFields = parts.slice(4);

        let parsedMessage = {
            raw: message,
            header: commandHeader,
            commandType: CommandType,
            imei: IMEI,
            payload: {},
            rawPayloadFields: payloadFields
        };
        switch (parsedMessage.commandType) {
            case 'Q0': // Check in command
                parsedMessage.payload = this._Command_Q0(payloadFields);
                break;
            case 'H0': // Heartbeat command
                parsedMessage.payload = this._Command_H0(payloadFields);
                break;
            case 'SD': // Get charging station status command
                parsedMessage.payload = this._Command_SD(payloadFields);
                break;
            default:
                parsedMessage.payload = this._parseGenericCommand(payloadFields);
                break;
        }

        return parsedMessage;
    }

    //////////////////////////    
    //IOT to SERVER COMMANDS//
    //////////////////////////


    /**
     * Check-in command - Q0
     *   IoT automatically sends this command as the first transmission upon
     *   every connection to the server (including reconnection after
     *   disconnection). No response is needed
     * @param {string[]} fields Payload.
     * @returns {object} 
     */
    _Command_Q0(fields) {
        try {
            if (fields.length < 1) {
                console.warn("Advertencia: El comando Q0 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            return {
                signal_strength: Math.round((fields[0] - 2) * 100 / 30),
                event: 'Connection'
            };
        } catch (error) {
            return {};
        }
    }

    /**
     * Heartbeat command - H0
     *   The lock transmits this command at regular intervals (default: every 4
     *   minutes) to maintain the connection with the server.
     * @param {string[]} fields Payload.
     * @returns {object} 
     */
    _Command_H0(fields) {
        try {
            if (fields.length < 1) {
                console.warn("Advertencia: El comando H0 tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            return {
                signal_strength: Math.round((fields[0] - 2) * 100 / 30),
                event: 'HeartBeat'
            };
        } catch (error) {
            return {};
        }
    }

    /**
    * Get charging station status - SD
    *   Get charging station status
    * @param {string[]} fields Payload.
    * @returns {object} 
    */
    _Command_SD(fields) {
        try {
            if (fields.length < 30) {
                console.warn("Advertencia: El comando SD tiene menos campos de los esperados.");
                return { raw: fields.join(this.FIELD_SEPARATOR) };
            }
            const statusLockStation1 = LockStationChargingStatus.fromArray(fields.slice(0, 15));
            const statusLockStation2 = LockStationChargingStatus.fromArray(fields.slice(15, 30));
            return {
                statusLockStation1: statusLockStation1,
                statusLockStation2: statusLockStation2
            };
        } catch (error) {
            return {};
        }
    }

    _parseGenericCommand(fields) {
        return { fields: fields };
    }
}
