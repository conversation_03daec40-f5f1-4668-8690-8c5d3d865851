import server from "./server.js";

const END_DELIMITER = '#';
const FIELD_SEPARATOR = ',';
const COMMAND_HEADER_SERVER_TO_IOT = '*SCOS';
const COMMAND_HEADER_IOT_TO_SERVER = '*SCOR';

function buildMessage(imei, commandType, payloadFields) {
    const payloadString = [COMMAND_HEADER_SERVER_TO_IOT, 'OM', imei, commandType, ...payloadFields].join(FIELD_SEPARATOR);
    return payloadString + END_DELIMITER + '\n'; // Add the newline at the end
}

async function _lockorUnlockRequestCommand(device, userid) {
    try {
        const message = buildMessage(device.serial, 'R0', [device.locking_parameters.lock ? 1 : 0, device.locking_parameters.validity, userid.toString(), device.locking_parameters.timestamp]);
        if (server.connectedClients.has(device.serial)) {
            const targetSocket = server.connectedClients.get(device.serial);
            await server.server.send(targetSocket, message);
            return { code: 200 };
        }
        else {
            device.connected = false;
            await device.save();
            return { code: 400, error: "Device not connected" };
        }

    } catch (error) {
        return { code: 401, error: error };
    }
}

async function _getLockStationIMEI(device) {
    try {
        const message = buildMessage(device.serial, 'SC', [0, 0]);
        if (server.connectedClients.has(device.serial)) {
            const targetSocket = server.connectedClients.get(device.serial);
            await server.server.send(targetSocket, message);
            return { code: 200 };
        }
        else {
            device.connected = false;
            await device.save();
            return { code: 400, error: "Device not connected" };
        }

    } catch (error) {
        return { code: 401, error: error };
    }


}
async function _configureLockStationIMEIReport(device, active) {
    try {
        let message;
        if (active) message = buildMessage(device.serial, 'SC', [1, 60]);
        else message = buildMessage(device.serial, 'SC', [0, 0]);
        if (server.connectedClients.has(device.serial)) {
            const targetSocket = server.connectedClients.get(device.serial);
            await server.server.send(targetSocket, message);
            return { code: 200 };
        }
        else {
            device.connected = false;
            await device.save();
            return { code: 400, error: "Device not connected" };
        }

    } catch (error) {
        return { code: 401, error: error };
    }


}

export default { _lockorUnlockRequestCommand, _getLockStationIMEI, _configureLockStationIMEIReport };
