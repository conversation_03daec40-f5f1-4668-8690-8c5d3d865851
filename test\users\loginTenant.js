import fetch from 'node-fetch';
import colors from '../colors.js';
import inputs from '../inputs.js';

export default async function loginTenant() {
  let startTimestamp;
  let finishTimestamp;
  console.log();
  console.log(colors.bg.black + colors.fg.green + "#############################################" + colors.reset);
  console.log(colors.bg.black + colors.fg.green + 'START TEST => LOGIN Tenant' + colors.reset);
  console.log(colors.bg.black + colors.fg.green + "#############################################" + colors.reset);
  console.log();

  const endpoint = "/users/login";
  const method = "POST";

  let Request = {
    username: inputs.TENANT.username,
    password: inputs.TENANT.password,
  };
  startTimestamp = Date.now();

  try {
    const response = await fetch('http://127.0.0.1:8080' + endpoint, {
      method: method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(Request)
    });
    const body = await response.text();
    if (process.env.DEBUGRESPONSES === 'true') {
      console.log('Backend_Response:');
      console.log(response.status + " : " + response.statusText + " - " + body)
      console.log();
    }
    finishTimestamp = Date.now();
    const TotalTime = finishTimestamp - startTimestamp;
    console.log('Response time: ' + TotalTime + " ms");

    const jwt = JSON.parse(body);
    console.log("JWT: " + jwt.accessToken)
    if (response.status != 200) return false;
    else return jwt.accessToken;
  } catch (error) {
    console.error('Error:', error);
    return false;
  }
}



