import authenticate from '../middlewares/authenticate.js';
import { default as LockStation, default as lockStation } from '../models/lockStations.js';
import lockStationModels from '../models/lockStationsModels.js';
import Station from '../models/station.js';

const newLockStation = async (req, res) => {
    try {
        const user = req.user;
        const new_lockstation = req.body;

        if (!authenticate.hasAuthority("Tenant", user)) return res.status(400).send({ error: "Only tenant users are allowed to create lock stations" });
        if (!new_lockstation.serial) return res.status(400).send({ error: "Serial not provided" });
        if (!new_lockstation.phone) return res.status(400).send({ error: "No phone provided" });
        if (!new_lockstation.modelId) return res.status(400).send({ error: "No model Id provided" });
        if (!new_lockstation.stationStartIndex) return res.status(400).send({ error: "No station start index provided" });

        const Model = await lockStationModels.findByPk(new_lockstation.modelId);
        if (!Model) return res.status(404).send({ error: "Model not found" });

        new_lockstation.tenantId = user.id;
        const newStation = await lockStation.create(new_lockstation);
        return res.status(200).json(newStation);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getLockStation = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (!id) return res.status(400).send({ error: "id not provided" });
        
        const LockStation = await lockStation.findByPk(id, {
            include: [
                {
                    model: lockStationModels,
                    as: 'relatedModel'
                },
                {
                    model: Station,
                    as: 'relatedStation'
                }

            ]

        });

        if (!LockStation) {
            return res.status(404).json({ error: "Lock Station not found" });
        }

        return res.status(200).json(LockStation);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const getAllLockStation = async (req, res) => {
    try {
        const user = req.user;

        var lockStations = {};
        if (authenticate.hasAuthority("Tenant", user)) {
            lockStations = await lockStation.findAll({
                where: {
                    tenantId: user.id,
                },
            });
        }
        else if (authenticate.hasAuthority("Customer", user)) {
            lockStations = await lockStation.findAll({
                where: {
                    tenantId: user.tenantId,
                },
            });
        }
        else if (authenticate.hasAuthority("Sysadmin", user)) {
            lockStations = await lockStation.findAll();
        }

        return res.status(200).json(lockStations);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const updateLockStation = async (req, res) => {
    try {
        const user = req.user;
        const update_lockStation = req.body;

        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to update lock stations" });
        if (!update_lockStation.id) return res.status(400).send({ error: "Lock stations id not provided" });

        const LockStations = await lockStation.findByPk(update_lockStation.id);
        if (!LockStations) return res.status(404).send({ error: "Lock station not found" });
        if (LockStations.tenantId != user.id) return res.status(400).send({ error: "You are not the owner of this lock Station" });


        if (update_lockStation.serial) {
            LockStations.serial = update_lockStation.serial;
        }
        if (update_lockStation.stationStartIndex) {
            LockStations.stationStartIndex = update_lockStation.stationStartIndex;
        }
        if (update_lockStation.phone) {
            LockStations.phone = update_lockStation.phone;
        }
        if (update_lockStation.description) {
            LockStations.description = update_lockStation.description;
        }
        if (update_lockStation.modelId) {
            LockStations.modelId = update_lockStation.modelId;
        }

        await LockStations.save();
        return res.status(200).json(LockStations);
    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const deleteLockStation = async (req, res) => {
    try {
        const user = req.user;
        const { id } = req.query;

        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to delete lock stations" });
        if (!id) return res.status(400).send({ error: "id not provided" });

        var LockStation = await lockStation.findByPk(id);
        if (!LockStation) return res.status(404).send({ error: "Model not found" });
        if (LockStation.tenantId != user.id) return res.status(400).send({ error: "Lock Station delete not permitted" });
        await LockStation.destroy();
        return res.status(200).send();

    } catch (error) {
        res.status(500).json({ error: error });
    }
};

const assingToStation = async (req, res) => {
    try {
        const user = req.user;
        const { stationId } = req.query;
        const { lockStationId } = req.query;

        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to assign lock stations" });
        if (!lockStationId) return res.status(400).send({ error: "Lock stations id not provided" });
        if (!stationId) return res.status(400).send({ error: "Station stations id not provided" });

        let LockStations = await lockStation.findByPk(lockStationId, {
            include:
                [
                    {
                        model: lockStationModels,
                        as: 'relatedModel'
                    },
                    {
                        model: Station,
                        as: 'relatedStation'
                    }
                ]
        });
        if (!LockStations) return res.status(404).send({ error: "Lock station not found" });
        if (LockStations.relatedStation) return res.status(400).send({ error: "Lock station already assign to a station" });
        if (LockStations.tenantId != user.id) return res.status(400).send({ error: "You are not the owner of this lock Station" });

        let _station = await Station.findByPk(stationId, {
            include:
                [
                    {
                        model: LockStation,
                        as: 'relatedLockStations',
                        include: {
                            model: lockStationModels,
                            as: 'relatedModel'
                        }
                    }
                ]
        });
        if (!_station) return res.status(404).send({ error: "Station not found" });

        if (_station.protocol != LockStations.relatedModel.protocol) return res.status(400).send({ error: "The lock station is not compatible with the station" });

        const firstIndex = LockStations.stationStartIndex;
        const lastIndex = LockStations.stationStartIndex + LockStations.relatedModel.spots - 1;
        for (const lock of _station.relatedLockStations) {
            const lockFirstIndex = lock.stationStartIndex;
            const lockLastIndex = lock.stationStartIndex + lock.relatedModel.spots - 1;
            if (firstIndex <= lockLastIndex && lockFirstIndex <= lastIndex) {
                return res.status(400).send({ error: "Two lock stations inside a station cannot have the same index" });
            }
        }

        _station.spots = _station.spots + LockStations.relatedModel.spots;
        _station.spots_availables = _station.spots_availables + LockStations.relatedModel.spots;
        LockStations.stationId = stationId;
        await _station.save();
        await LockStations.save();
        LockStations.dataValues.relatedStation = _station.dataValues;

        return res.status(200).json(LockStations);
    } catch (error) {
        res.status(500).json({ error: error });
    }


}

const unassignFromStation = async (req, res) => {
    try {
        const user = req.user;
        const { stationId } = req.query;
        const { lockStationId } = req.query;

        if (user.type != 'Tenant') return res.status(400).send({ error: "Only tenant users are allowed to assign lock stations" });
        if (!lockStationId) return res.status(400).send({ error: "Lock stations id not provided" });
        if (!stationId) return res.status(400).send({ error: "Station stations id not provided" });

        let _station = await Station.findByPk(stationId);
        if (!_station) return res.status(404).send({ error: "Station not found" });
        let LockStations = await lockStation.findByPk(lockStationId, {
            include:
                [
                    {
                        model: lockStationModels,
                        as: 'relatedModel'
                    },
                    {
                        model: Station,
                        as: 'relatedStation'
                    }
                ]
        }
        );
        if (!LockStations) return res.status(404).send({ error: "Lock station not found" });
        if (LockStations.tenantId != user.id) return res.status(400).send({ error: "You are not the owner of this lock Station" });

        if (!LockStations.relatedStation) return res.status(400).send({ error: "Lock station is not assign to a station" });
        if (LockStations.stationId == stationId) LockStations.stationId = null;
        else return res.status(400).send({ error: "Lock station doesnt belong to the station id provided" })
        _station.spots = _station.spots - LockStations.relatedModel.spots;
        _station.spots_availables = _station.spots_availables - LockStations.relatedModel.spots;
        await _station.save();
        LockStations.dataValues.relatedStation = null;
        await LockStations.save();
        return res.status(200).json(LockStations);
    } catch (error) {
        res.status(500).json({ error: error });
    }

}

const getLastTelemetry = async (req, res) => {
}

const getTelemetries = async (req, res) => {
}

const getPaymentsInfo = async (req, res) => {
}

export default {
    newLockStation, getLockStation, deleteLockStation, updateLockStation,
    getAllLockStation, assingToStation, unassignFromStation,
    getLastTelemetry, getTelemetries, getPaymentsInfo
};
