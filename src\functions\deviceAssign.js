import validate from "../middlewares/validate.js";
import Device from "../models/device.js";
import DevicesModel from "../models/deviceModels.js";
import LockStation from "../models/lockStations.js";
import lockStationModels from '../models/lockStationsModels.js';
import Station from "../models/station.js";

/**
 * Return a device to a station
 * 
 * @param {number} userId user id
 * @param {String} deviceId device id
 * @param {String} lockStationId lock station id
 * @param {number} lockStationIndex lock station index
 * @throws {Error} if the operation fails
 */
const returnDeviceToStation = async (userId, deviceId, lockStationId, lockStationIndex) => {
    try {
        if (!deviceId) throwError("No device id provided");
        if (!lockStationId) throwError("Lock station id not provided");
        if (!lockStationIndex) throwError("Lock station index not provided");
        if (!validate.isUUID(deviceId)) throwError("Device id provided is not an uuid")
        
        var device = await Device.findByPk(deviceId, {
            include: [
                {
                    model: DevicesModel,
                    as: 'relatedModel'
                }
            ]
        });
        if (!device) throwError("Device not found", 404);
        if (device.LockStationId) throwError("The device is already assigned");

        var lockStation = await LockStation.findByPk(lockStationId, {
            include:
                [
                    {
                        model: lockStationModels,
                        as: 'relatedModel'
                    },
                    {
                        model: Station,
                        as: 'relatedStation'
                    }
                ]
        });
        if (!lockStation) throwError("Lock station not found", 404);
        if (lockStation.tenantId != userId) throwError("You are not the owner of this lock Station");
        if (lockStationIndex > lockStation.relatedModel.spots) throwError("Invalid lock station index");
        if (lockStation.relatedModel.protocol != device.relatedModel.protocol) throwError("The device is not compatible with the lock station");

        var station = lockStation.relatedStation;
        if (!station) throwError("Lock station is not assign to a station");
        station.spots_availables = station.spots_availables - 1;

        device.customerId = null;
        device.LockStationId = lockStation.id;
        device.stationIndex = lockStation.stationStartIndex + lockStationIndex - 1; // Starts at 1
        device.status = "Ready";
        device.locking_parameters = {};
        
        await device.save();
        await station.save();
    } catch (error) {
        throwError(error.message || "Internal server error", error.statusCode || 500);
    }
};

/**
 * Return a device to a station
 * 
 * @param {number} userId user id
 * @param {String} deviceId device id
 * @throws {Error} if the operation fails
 */
const assignDeviceToUser = async (userId, deviceId) => {
    try {
        if (!deviceId) throwError("No device id provided");
        if (!validate.isUUID(deviceId)) throwError("Device id provided is not an uuid")
            
        var device = await Device.findByPk(deviceId);
        if (!device) throwError("Device not found", 404);
        if (!device.LockStationId) throwError("The device is not assigned");

        var lockStation = await LockStation.findByPk(device.LockStationId, {
            include:
                [
                    {
                        model: Station,
                        as: 'relatedStation'
                    }
                ]
        });
        if (!lockStation) throwError("Lock station not found", 404);
        if (lockStation.tenantId != userId) throwError("You are not the owner of this lock Station");

        var station = lockStation.relatedStation;
        if (!station) throwError("Lock station is not assign to a station");
        station.spots_availables = station.spots_availables + 1;

        device.customerId = userId;
        device.LockStationId = null;
        device.stationIndex = null;
        device.status = "Ready";
        device.locking_parameters = {};
        
        await device.save();
        await station.save();
    } catch (error) {
        throwError(error.message || "Internal server error", error.statusCode || 500);
    }
};

function throwError(message, statusCode = 400) {
    const error = new Error(message);
    error.statusCode = statusCode;
    throw error;
}

export default { returnDeviceToStation, assignDeviceToUser };