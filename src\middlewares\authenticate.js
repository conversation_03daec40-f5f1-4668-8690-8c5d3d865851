import jwt from 'jsonwebtoken';
import UserModel from '../models/users.js';
const  authenticateToken = async (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    try {
        if (!token) {
            return res.status(401).json({ error: 'Access denied. Token not provided.' });
        }
    
        jwt.verify(token, process.env.TOKENSECRET, async (err, user) => {
            if (err) {
                return res.status(401).json({ error: 'Invalid or expired token.' });
            }
            req.user = await UserModel.findByPk(user.id);
            if(!req.user)return res.status(400).send({error:"Invalid user"});
            if(req.user.deleted == true)return res.status(400).send({error:"Invalid user"});
            next();
        });    
    } catch (error) {
        return res.status(500).send({error: "internal server error"});
    }    
};

async function isDeleted(req, res, next){
    const user = await UserModel.findByPk(req.user.id);
    if(user.deleted == true)return res.status(400).send({error:"Invalid user"});
    req.user = user;
    next();  
}

function hasAuthority(level, user){
    try {
        if(user?.type == level)return true;
        else return false;
        
    } catch (error) {
        return false;
    } 
}

export default {authenticateToken, isDeleted,hasAuthority};
