import uuid


def buildpaymentdetails(amount: float) -> dict:
    payment_amount_in_cents = amount * 100  # Convert to cents

    payment_details_dict = {
        "customer": {"id": "customer39911805", "email": "<EMAIL>"},
        "user_id": "customer",
        "site_transaction_id": str(uuid.uuid4()),
        "payment_method_id": 105,  # Example: Visa Crédito
        "amount": payment_amount_in_cents,
        "currency": "ARS",
        "installments": 1,
        "description": "eBike trip",
        "payment_type": "single",
        "sub_payments": [],
        "fraud_detection": {"send_to_cs": False},
    }

    return payment_details_dict
