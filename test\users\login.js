import fetch from 'node-fetch';
import colors from '../colors.js';
import inputs from '../inputs.js';

export default async function loginUser() {
  let startTimestamp;
  let finishTimestamp;
  console.log();
  console.log(colors.bg.black + colors.fg.green + "#############################################" + colors.reset);
  console.log(colors.bg.black + colors.fg.green + 'START TEST => LOGIN USER' + colors.reset);
  console.log(colors.bg.black + colors.fg.green + "#############################################" + colors.reset);
  console.log();

  const endpoint = "/users/login";
  const method = "POST";

  let Request = {
    username: inputs.username,
    password: inputs.password,
  };
  startTimestamp = Date.now();

  try {
    const response = await fetch('http://127.0.0.1:8080' + endpoint, {
      method: method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(Request)
    });
    const body = await response.text();
    if (process.env.DEBUGRESPONSES === 'true') {
      console.log('Backend_Response:');
      console.log(response.status + " : " + response.statusText + " - " + body)
      console.log();
    }
    finishTimestamp = Date.now();
    const TotalTime = finishTimestamp - startTimestamp;
    console.log('Response time: ' + TotalTime + " ms");

    const jwt = JSON.parse(body);
    console.log("JWT: " + jwt.accessToken)
    if (response.status != 200) return false;
    else return jwt.accessToken;
  } catch (error) {
    console.error('Error:', error);
    return false;
  }
}



