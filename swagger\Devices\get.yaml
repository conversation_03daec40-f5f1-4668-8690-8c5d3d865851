  # -------------> Get Device by Id <-------------
paths:
  "/devices":
    get:
      tags:
        - Devices
      summary: Get a device by ID
      parameters:
        - name: id
          in: query
          description: Device Id
          required: true
          schema:
            type: string
            example: "11e88513-a7f5-46f1-b150-0b0dceb3e1a5"
      responses:
        200:
          description: Device found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
        400:
          description: Bad request
        401:
          description: Unauthorized
        404:
          description: Not found
        500:
          description: Internal server error